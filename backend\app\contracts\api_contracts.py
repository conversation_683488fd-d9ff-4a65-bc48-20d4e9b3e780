"""
API Contracts using Pydantic for type safety and validation.
These contracts ensure data integrity across the entire pipeline.
"""
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from enum import Enum

class LogLevel(str, Enum):
    """Log level enumeration"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"
    SUCCESS = "SUCCESS"

class ExecuteStepRequest(BaseModel):
    """Request model for pipeline step execution"""
    step_id: str = Field(..., description="Pipeline step identifier")
    file_id: str = Field(..., description="File processing identifier")
    run_id: str = Field(..., description="Pipeline run identifier")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Step-specific parameters")

class PipelineStepResult(BaseModel):
    """Result model for pipeline step execution"""
    status: str = Field(..., description="Execution status (success/error)")
    message: str = Field(..., description="Human-readable result message")
    step_id: str = Field(..., description="Pipeline step identifier")
    output_files: List[str] = Field(default_factory=list, description="Generated output file paths")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional result metadata")
    error_details: Optional[str] = Field(None, description="Detailed error information if status is error")

class LogMessage(BaseModel):
    """Log message contract"""
    level: LogLevel = Field(..., description="Log level")
    message: str = Field(..., description="Log message content")
    step_id: str = Field(..., description="Pipeline step identifier")
    module: str = Field(..., description="Source module name")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional log metadata")
    data_block: Optional[str] = Field(None, description="Formatted content block for complex output")

class ProcessingParameters(BaseModel):
    """Parameters for document processing"""
    use_auto_toc: bool = True
    count_token: bool = True
    skip_llm: bool = False
    output_format: str = "ecommerce"

class BusinessFlowParameters(BaseModel):
    """Parameters for business flow generation"""
    max_flows: int = Field(default=15, ge=1, le=50, description="Maximum number of business flows to generate")
    count_token: bool = Field(default=True, description="Enable token counting and cost calculation")
    
class RelevantContentParameters(BaseModel):
    """Parameters for relevant content processing"""
    max_workers: int = Field(default=3, ge=1, le=10, description="Maximum number of worker threads")
    count_token: bool = Field(default=True, description="Enable token counting and cost calculation")
    enable_caching: bool = Field(default=True, description="Enable caching for relevant content")
    
class PathFinderParameters(BaseModel):
    """Parameters for screen path generation"""
    max_path_depth: int = Field(default=5, ge=1, le=20, description="Maximum depth for path generation")
    count_token: bool = Field(default=True, description="Enable token counting and cost calculation")
    
class PathToCsvParameters(BaseModel):
    """Parameters for path to CSV conversion"""
    include_all_combinations: bool = Field(default=True, description="Include all possible combinations")
    max_combinations_per_flow: int = Field(default=50, ge=1, le=200, description="Maximum combinations per business flow")
    count_token: bool = Field(default=True, description="Enable token counting and cost calculation")
    
class TestCaseGenerationParameters(BaseModel):
    """Parameters for test case generation"""
    include_negative_tests: bool = Field(default=True, description="Include negative test scenarios")
    test_data_variants: int = Field(default=3, ge=1, le=10, description="Number of test data variants per test case")
    count_token: bool = Field(default=True, description="Enable token counting and cost calculation")
    
class TestCaseEvaluationParameters(BaseModel):
    """Parameters for test case evaluation"""
    count_token: bool = Field(default=True, description="Enable token counting and cost calculation")

class FileUploadResponse(BaseModel):
    """Response model for file upload"""
    file_id: str = Field(..., description="Unique file processing identifier")
    filename: str = Field(..., description="Original filename")
    status: str = Field(..., description="Upload status")
    message: str = Field(..., description="Upload result message")

class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service health status")
    timestamp: str = Field(..., description="Check timestamp")
    version: Optional[str] = Field(None, description="Service version")
