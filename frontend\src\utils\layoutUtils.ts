import dagre from 'dagre'
import { Node, Edge, Position } from 'reactflow'

const dagreGraph = new dagre.graphlib.Graph()
dagreGraph.setDefaultEdgeLabel(() => ({}))

const nodeWidth = 280
const nodeHeight = 180

export const getLayoutedElements = (
  nodes: Node[],
  edges: Edge[],
  direction = 'TB'
): { nodes: Node[]; edges: Edge[] } => {
  const isHorizontal = direction === 'LR'
  
  // Configure the dagre graph for 2-column layout
  dagreGraph.setGraph({
    rankdir: direction,
    nodesep: 200,      // Increased horizontal spacing for 2-column layout
    ranksep: 120,      // Reduced vertical spacing to encourage column formation
    marginx: 50,       // Graph margin
    marginy: 50,
    align: 'UL',       // Align nodes to upper-left for consistent positioning
    acyclicer: 'greedy', // Use greedy algorithm for better layout
    ranker: 'tight-tree' // Use tight-tree ranker for more compact layout
  })

  // Clear previous graph data
  nodes.forEach((node) => {
    if (dagreGraph.hasNode(node.id)) {
      dagreGraph.removeNode(node.id)
    }
  })

  // Add nodes to dagre graph
  nodes.forEach((node) => {
    dagreGraph.setNode(node.id, { 
      width: nodeWidth, 
      height: nodeHeight 
    })
  })

  // Add edges to dagre graph
  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target)
  })

  // Calculate layout
  dagre.layout(dagreGraph)

  // Apply calculated positions to nodes
  const layoutedNodes: Node[] = nodes.map((node) => {
    const nodeWithPosition = dagreGraph.node(node.id)
    
    return {
      ...node,
      targetPosition: isHorizontal ? Position.Left : Position.Top,
      sourcePosition: isHorizontal ? Position.Right : Position.Bottom,
      position: {
        x: nodeWithPosition.x - nodeWidth / 2,
        y: nodeWithPosition.y - nodeHeight / 2,
      },
    }
  })

  // Return layouted elements
  return { 
    nodes: layoutedNodes, 
    edges: edges.map(edge => ({
      ...edge,
      type: edge.type || 'smoothstep',
      animated: edge.animated ?? true,
      style: {
        stroke: '#6B7280',
        strokeWidth: 2,
        ...edge.style
      }
    }))
  }
}

// Alternative layout directions
export const getHorizontalLayout = (nodes: Node[], edges: Edge[]) => 
  getLayoutedElements(nodes, edges, 'LR')

export const getVerticalLayout = (nodes: Node[], edges: Edge[]) => 
  getLayoutedElements(nodes, edges, 'TB')
