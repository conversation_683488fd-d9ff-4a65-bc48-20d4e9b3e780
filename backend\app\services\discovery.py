"""
Pipeline discovery service for scanning and building DAG structure
"""
import os
import importlib.util
import sys
from typing import Dict, List, Any, Tu<PERSON>
from pathlib import Path
import logging

from ..core.decorators import get_pipeline_steps, PIPELINE_STEPS_REGISTRY
from ..core.config import settings

logger = logging.getLogger(__name__)

class PipelineDiscoveryService:
    """Service for discovering pipeline steps and building DAG structure"""
    
    def __init__(self):
        self.pipelines_dir = settings.pipelines_dir
        
    def discover_pipeline_dag(self) -> Dict[str, Any]:
        """
        Discover all pipeline steps and build DAG structure
        
        Returns:
            Dictionary containing nodes and edges for the DAG
        """
        # Clear existing registry
        PIPELINE_STEPS_REGISTRY.clear()
        
        # Scan pipeline files
        self._scan_pipeline_files()
        
        # Get all discovered steps
        steps = get_pipeline_steps()
        
        # Build nodes and edges
        nodes = self._build_nodes(steps)
        edges = self._build_edges(steps)
        
        return {
            "nodes": nodes,
            "edges": edges,
            "metadata": {
                "total_steps": len(nodes),
                "total_connections": len(edges)
            }
        }
    
    def _scan_pipeline_files(self):
        """Scan all Python files in the pipelines directory"""
        if not os.path.exists(self.pipelines_dir):
            logger.warning(f"Pipelines directory not found: {self.pipelines_dir}")
            return
        
        for root, dirs, files in os.walk(self.pipelines_dir):
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    file_path = os.path.join(root, file)
                    self._import_pipeline_file(file_path)
    
    def _import_pipeline_file(self, file_path: str):
        """Import a pipeline file to register its decorated functions"""
        try:
            # Create module name from file path
            rel_path = os.path.relpath(file_path, self.pipelines_dir)
            module_name = rel_path.replace(os.sep, '.').replace('.py', '')
            module_name = f"pipelines.{module_name}"
            
            # Import the module
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                sys.modules[module_name] = module
                spec.loader.exec_module(module)
                logger.info(f"Imported pipeline module: {module_name}")
        except Exception as e:
            logger.error(f"Failed to import pipeline file {file_path}: {e}")
    
    def _build_nodes(self, steps: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Build node list for the DAG in correct workflow order"""
        nodes = []

        # Define the correct workflow order
        workflow_order = [
            "pipelines.document_processor.process_document",           # Step 1
            "pipelines.diagram_processor.process_diagrams",           # Step 1.1 (Optional)
            "pipelines.business_flow_generator.generate_business_flows", # Step 2
            "pipelines.relevant_content_processor.process_relevant_content", # Step 3
            "pipelines.screen_path_generator.generate_screen_paths",   # Step 4
            "pipelines.test_case_generator.generate_test_cases",       # Step 5
            "pipelines.final_test_case_generator.generate_final_test_cases", # Step 6
            "pipelines.test_case_evaluator.evaluate_test_cases"       # Step 8
        ]

        # Add steps in the correct order
        for step_id in workflow_order:
            if step_id in steps:
                step_info = steps[step_id]
                node = {
                    "id": step_id,
                    "type": "custom",
                    "position": {"x": 0, "y": 0},  # Will be auto-layouted by React Flow
                    "data": {
                        "label": step_info["name"],
                        "description": step_info["description"],
                        "status": "pending",  # pending, running, success, failed
                        "inputs": step_info["inputs"],
                        "outputs": step_info["outputs"],
                        "parameters": step_info["parameters"],
                        "configurable_params": step_info["configurable_params"]
                    }
                }
                nodes.append(node)

        # Add any remaining steps that weren't in the workflow order
        for step_id, step_info in steps.items():
            if step_id not in workflow_order:
                node = {
                    "id": step_id,
                    "type": "custom",
                    "position": {"x": 0, "y": 0},
                    "data": {
                        "label": step_info["name"],
                        "description": step_info["description"],
                        "status": "pending",
                        "inputs": step_info["inputs"],
                        "outputs": step_info["outputs"],
                        "parameters": step_info["parameters"],
                        "configurable_params": step_info["configurable_params"]
                    }
                }
                nodes.append(node)

        return nodes
    
    def _build_edges(self, steps: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Build edge list for the DAG based on input/output relationships"""
        edges = []
        edge_id = 0
        
        # Create a mapping of outputs to steps
        output_to_step = {}
        for step_id, step_info in steps.items():
            for output in step_info["outputs"]:
                if output not in output_to_step:
                    output_to_step[output] = []
                output_to_step[output].append(step_id)
        
        # Create edges based on input/output relationships
        for step_id, step_info in steps.items():
            for input_artifact in step_info["inputs"]:
                # Find steps that produce this input
                if input_artifact in output_to_step:
                    for source_step in output_to_step[input_artifact]:
                        if source_step != step_id:  # Avoid self-loops
                            edge = {
                                "id": f"edge-{edge_id}",
                                "source": source_step,
                                "target": step_id,
                                "type": "smoothstep",
                                "animated": False,
                                "data": {
                                    "artifact": input_artifact
                                }
                            }
                            edges.append(edge)
                            edge_id += 1
        
        return edges
    
    def get_step_details(self, step_id: str) -> Dict[str, Any]:
        """Get detailed information about a specific step"""
        steps = get_pipeline_steps()
        return steps.get(step_id, {})

# Global instance
discovery_service = PipelineDiscoveryService()
