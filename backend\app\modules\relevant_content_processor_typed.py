"""
Type-hinted Relevant Content Processor Module
Provides strict typing and contracts for relevant content processing functionality.
"""
import os
import sys
import logging
import importlib.util
from typing import Dict, Any, Optional
from pathlib import Path

from ..contracts.api_contracts import RelevantContentParameters, PipelineStepResult
from ..core.log_emitter import LogEmitter

# Get the root directory for imports
root_dir = Path(__file__).parent.parent.parent.parent

async def execute(
    params: RelevantContentParameters,
    logger: LogEmitter,
    run_id: str,
    business_flows_path: str,
    output_dir: str,
    document_structure_path: Optional[str] = None,
    **kwargs
) -> PipelineStepResult:
    """
    Process relevant content using the user's RelevantContentProcessor class.
    
    Args:
        business_flows_path: Path to business flows directory or file
        output_dir: Dynamic output directory for this session
        logger: LogEmitter instance for centralized logging
        run_id: Pipeline run identifier
        document_structure_path: Optional path to document structure file
        max_workers: Maximum number of worker threads (1-10)
        count_token: Whether to count tokens and calculate costs
        enable_caching: Whether to enable caching for relevant content
        **kwargs: Additional parameters
        
    Returns:
        PipelineStepResult: Structured result with status, message, and output files
        
    Raises:
        FileNotFoundError: If required input files are not found
        ImportError: If RelevantContentProcessor class cannot be imported
        ValueError: If parameters are invalid
    """
    step_id = kwargs.get("step_id", "relevant_content_processor")

    try:
        # Parameters are already validated as Pydantic model
        
        await logger.log(run_id, "INFO", "🚀 Starting Relevant Content Processing...", step_id)
        
        # Log configuration as a block
        config_info = f"""📋 Relevant Content Configuration:
   • Business flows path: {business_flows_path}
   • Output directory: {output_dir}
   • Max workers: {params.max_workers}
   • Count tokens: {params.count_token}
   • Enable caching: {params.enable_caching}"""
        await logger.log_block(run_id, "Configuration", config_info, step_id)
        
        # Setup paths using provided output_dir
        relevant_content_dir = os.path.join(output_dir, "relevant_content_processor")
        os.makedirs(relevant_content_dir, exist_ok=True)
        
        await logger.log(run_id, "INFO", f"📁 Created relevant content directory: {relevant_content_dir}", step_id)
        
        # Find required input files
        # 1. Document structure (merged JSON)
        merged_json = os.path.join(output_dir, "document_processor", "merged_output.json")
        if not os.path.exists(merged_json):
            # Try alternative paths
            alt_paths = [
                document_structure_path,
                os.path.join(output_dir, "merged_output.json"),
                os.path.join(output_dir, "document_processor", "merged_output.json")
            ]
            
            for alt_path in alt_paths:
                if alt_path and os.path.exists(alt_path):
                    merged_json = alt_path
                    break
            else:
                await logger.log(run_id, "ERROR", f"❌ Document structure file not found", step_id)
                raise FileNotFoundError("Document structure file not found")
        
        await logger.log(run_id, "INFO", f"📄 Using document structure: {merged_json}", step_id)
        
        # 2. Business flows directory
        business_flows_dir = business_flows_path
        if not os.path.isdir(business_flows_dir):
            business_flows_dir = os.path.join(output_dir, "business_flows")
        
        if not os.path.exists(business_flows_dir):
            await logger.log(run_id, "ERROR", f"❌ Business flows directory not found: {business_flows_dir}", step_id)
            raise FileNotFoundError(f"Business flows directory not found: {business_flows_dir}")
        
        await logger.log(run_id, "INFO", f"📁 Using business flows directory: {business_flows_dir}", step_id)
        
        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))
        
        # Import and use the user's RelevantContentProcessor class
        await logger.log(run_id, "INFO", "📦 Importing RelevantContentProcessor class...", step_id)
        
        try:
            # Try different import approaches
            try:
                from back_end.relevant_content_processor import RelevantContentProcessor
                await logger.log(run_id, "SUCCESS", "✅ Successfully imported RelevantContentProcessor (direct import)", step_id)
            except ImportError:
                # Fallback to dynamic import
                await logger.log(run_id, "INFO", "   Trying dynamic import...", step_id)
                spec = importlib.util.spec_from_file_location(
                    "relevant_content_processor",
                    os.path.join(root_dir, "back_end", "relevant_content_processor.py")
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                RelevantContentProcessor = module.RelevantContentProcessor
                await logger.log(run_id, "SUCCESS", "✅ Successfully imported RelevantContentProcessor (dynamic import)", step_id)
                
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to import RelevantContentProcessor: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Failed to import RelevantContentProcessor: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Setup configuration file path
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")
        
        # Initialize the user's RelevantContentProcessor
        await logger.log(run_id, "INFO", "🔧 Initializing RelevantContentProcessor...", step_id)
        
        try:
            processor = RelevantContentProcessor(
                business_flows_dir=business_flows_dir,
                document_structure_path=merged_json,
                output_dir=relevant_content_dir,
                config_file=config_file,
                max_workers=params.max_workers,
                count_token=params.count_token,
                enable_caching=params.enable_caching
            )
            await logger.log(run_id, "SUCCESS", "✅ RelevantContentProcessor initialized successfully", step_id)
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to initialize RelevantContentProcessor: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Failed to initialize RelevantContentProcessor: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Execute relevant content processing
        await logger.log(run_id, "INFO", "🚀 Starting relevant content processing...", step_id)
        await logger.log(run_id, "INFO", f"   Using {params.max_workers} worker threads...", step_id)
        
        try:
            # Call the execute method
            result = processor.execute()
            
            if result and result.get("status") == "success":
                await logger.log(run_id, "SUCCESS", "✅ Relevant content processing completed successfully", step_id)
                
                # Log output files
                output_files = []
                if os.path.exists(relevant_content_dir):
                    output_files = [os.path.join(relevant_content_dir, f) for f in os.listdir(relevant_content_dir)]
                    if output_files:
                        files_info = "\n".join([f"   • {os.path.basename(f)}" for f in output_files])
                        await logger.log_block(run_id, "Generated Files", f"📄 Output Files:\n{files_info}", step_id)
                
                return PipelineStepResult(
                    status="success",
                    message="Relevant content processing completed successfully",
                    step_id=step_id,
                    output_files=output_files,
                    metadata={
                        "max_workers": params.max_workers,
                        "count_token": params.count_token,
                        "enable_caching": params.enable_caching
                    }
                )
            else:
                error_msg = result.get("message", "Unknown error") if result else "No result returned"
                await logger.log(run_id, "ERROR", f"❌ Relevant content processing failed: {error_msg}", step_id)
                return PipelineStepResult(
                    status="error",
                    message=f"Relevant content processing failed: {error_msg}",
                    step_id=step_id,
                    error_details=str(result) if result else "No result returned"
                )
                
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Exception during relevant content processing: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Exception during relevant content processing: {e}",
                step_id=step_id,
                error_details=str(e)
            )
            
    except Exception as e:
        await logger.log(run_id, "ERROR", f"❌ Unexpected error in relevant content processing: {e}", step_id)
        return PipelineStepResult(
            status="error",
            message=f"Unexpected error: {e}",
            step_id=step_id,
            error_details=str(e)
        )
