"""
Data contracts using Pydantic models for type safety and validation.
These models act as enforceable contracts between different parts of the system.
"""
from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel, Field, field_validator
from enum import Enum

class NodeStatus(str, Enum):
    """Pipeline node status enumeration"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"
    PENDING = "pending"

class LogLevel(str, Enum):
    """Log level enumeration"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ExecuteStepRequest(BaseModel):
    """Contract for execute-step endpoint request body"""
    step_id: str = Field(..., description="Unique identifier for the pipeline step")
    file_id: str = Field(..., description="File processing ID from upload")
    run_id: str = Field(..., description="Unique run identifier for this execution")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Step-specific parameters")
    
    @field_validator('step_id')
    @classmethod
    def validate_step_id(cls, v):
        """Validate step_id follows expected pattern"""
        if not v or len(v.strip()) == 0:
            raise ValueError('step_id cannot be empty')
        return v.strip()
    
    @field_validator('run_id')
    @classmethod
    def validate_run_id(cls, v):
        """Validate run_id follows expected pattern"""
        if not v or len(v.strip()) == 0:
            raise ValueError('run_id cannot be empty')
        return v.strip()

class ExecuteStepResponse(BaseModel):
    """Contract for execute-step endpoint response"""
    success: bool
    message: str
    run_id: str
    step_id: str
    output_files: Optional[List[str]] = None
    error_details: Optional[str] = None
    execution_time_seconds: Optional[float] = None

class ProcessingParameters(BaseModel):
    """Contract for document processing parameters"""
    skip_llm: bool = Field(default=False, description="Skip LLM analysis for faster processing")
    count_token: bool = Field(default=True, description="Track token usage for cost calculation")
    use_auto_toc: bool = Field(default=True, description="Automatically generate table of contents")
    output_format: str = Field(default="ecommerce", description="Output format type")
    enable_caching: bool = Field(default=True, description="Enable caching for performance")

class LogMessage(BaseModel):
    """Contract for log messages"""
    timestamp: str
    level: LogLevel
    module: str
    message: str
    run_id: Optional[str] = None
    step_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class TokenUsage(BaseModel):
    """Contract for token usage tracking"""
    input_tokens: int = Field(ge=0, description="Number of input tokens")
    output_tokens: int = Field(ge=0, description="Number of output tokens")
    total_tokens: int = Field(ge=0, description="Total tokens used")
    cost_usd: float = Field(ge=0.0, description="Estimated cost in USD")
    model_type: str = Field(..., description="Model type used (e.g., 'gemini-pro')")

class PipelineStepResult(BaseModel):
    """Contract for pipeline step execution results"""
    step_id: str
    status: NodeStatus
    start_time: str
    end_time: Optional[str] = None
    output_files: List[str] = Field(default_factory=list)
    error_message: Optional[str] = None
    token_usage: Optional[TokenUsage] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

class FileUploadResponse(BaseModel):
    """Contract for file upload response"""
    success: bool
    file_id: str
    filename: str
    file_size: int
    upload_time: str
    error_message: Optional[str] = None

class PipelineNode(BaseModel):
    """Model for pipeline DAG node"""
    id: str
    type: str
    position: Dict[str, float]
    data: Dict[str, Any]

class PipelineEdge(BaseModel):
    """Model for pipeline DAG edge"""
    id: str
    source: str
    target: str
    type: str
    animated: bool = False
    data: Optional[Dict[str, Any]] = None

class PipelineGraphResponse(BaseModel):
    """Response model for pipeline graph"""
    nodes: List[PipelineNode]
    edges: List[PipelineEdge]
    metadata: Dict[str, Any]

class FileInfo(BaseModel):
    """Model for file information"""
    name: str
    path: str
    size: int
    modified: float

class WorkspaceFilesResponse(BaseModel):
    """Response model for workspace files"""
    files: List[FileInfo]
    directories: List[Dict[str, str]]

class WebSocketMessage(BaseModel):
    """Model for WebSocket messages"""
    type: str  # step_update, terminal_output, cost_update
    data: Dict[str, Any]