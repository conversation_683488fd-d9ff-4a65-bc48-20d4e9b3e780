// Master Pipeline Configuration - Source of Truth for Backend Architecture
export interface PipelineNode {
  id: string
  name: string
  description: string
  inputs: string[]
  outputs: string[]
  stepNumber: number
  subSteps?: {
    id: string
    name: string
    description: string
  }[]
  estimatedTime?: string
  complexity?: 'low' | 'medium' | 'high'
}

export interface PipelineEdge {
  id: string
  source: string
  target: string
  data?: {
    artifact: string
    description?: string
  }
}

export interface PipelineConfig {
  nodes: PipelineNode[]
  edges: PipelineEdge[]
}

// Master Pipeline Configuration - Matches your exact backend architecture
export const MASTER_PIPELINE_CONFIG: PipelineConfig = {
  nodes: [
    {
      id: 'document_processor',
      name: '1. Document Processor',
      description: 'Reads PDF, extracts text/TOC, creates structured JSON output',
      inputs: ['pdf_file'],
      outputs: ['merged_output.json', 'description_output.json'],
      stepNumber: 1,
      estimatedTime: '30-60s',
      complexity: 'medium'
    },
    {
      id: 'extract_and_process_to_json',
      name: '2. Image & Diagram Extractor',
      description: 'Extracts images/diagrams from PDF, uses Gemini to analyze them into structured JSON',
      inputs: ['pdf_file'],
      outputs: ['diagram_analysis.json', 'extracted_images/'],
      stepNumber: 2,
      estimatedTime: '45-90s',
      complexity: 'high'
    },
    {
      id: 'business_flow_detector',
      name: '3. Business Flow Detector',
      description: 'Uses LLM on merged_output.json to identify high-level business flows',
      inputs: ['merged_output.json'],
      outputs: ['business_flows.txt'],
      stepNumber: 3,
      estimatedTime: '20-40s',
      complexity: 'medium'
    },
    {
      id: 'relevant_content_processor',
      name: '4. Relevant Content Processor',
      description: 'Filters relevant document sections for specific business flows',
      inputs: ['business_flows.txt', 'merged_output.json'],
      outputs: ['screen_graph.json', 'filtered_content.json'],
      stepNumber: 4,
      estimatedTime: '15-30s',
      complexity: 'medium'
    },
    {
      id: 'path_finder',
      name: '5. Path Finder',
      description: 'Defines screen paths and finds valid paths using BFS algorithm',
      inputs: ['screen_graph.json'],
      outputs: ['screen_paths.json', 'path_analysis.json'],
      stepNumber: 5,
      estimatedTime: '10-25s',
      complexity: 'medium',
      subSteps: [
        {
          id: 'path_processor',
          name: 'Path Processor',
          description: 'Defines screens, variables, and navigation rules'
        },
        {
          id: 'bfs_pathfinder',
          name: 'BFS Pathfinder',
          description: 'Finds shortest valid paths between screens using BFS'
        }
      ]
    },
    {
      id: 'path_to_csv',
      name: '6. Test Data Generator',
      description: 'Flattens path data and generates test data combinations using All-Pairs technique',
      inputs: ['screen_paths.json'],
      outputs: ['test_data.csv', 'data_combinations.csv'],
      stepNumber: 6,
      estimatedTime: '5-15s',
      complexity: 'low'
    },
    {
      id: 'gen_test_case',
      name: '7. Test Case Generator',
      description: 'Reads CSV, builds prompts, and uses Gemini to generate detailed test cases',
      inputs: ['test_data.csv'],
      outputs: ['test_cases.json', 'test_scenarios.json'],
      stepNumber: 7,
      estimatedTime: '60-120s',
      complexity: 'high'
    },
    {
      id: 'test_case_evaluator',
      name: '8. Test Case Evaluator',
      description: 'Uses LLM to evaluate generated test cases against requirements and benchmarks',
      inputs: ['test_cases.json', 'requirements.json'],
      outputs: ['evaluation_report.json', 'quality_metrics.json'],
      stepNumber: 8,
      estimatedTime: '30-60s',
      complexity: 'medium'
    }
  ],
  edges: [
    {
      id: 'e1-3',
      source: 'document_processor',
      target: 'business_flow_detector',
      data: {
        artifact: 'merged_output.json',
        description: 'Structured document content'
      }
    },
    {
      id: 'e3-4',
      source: 'business_flow_detector',
      target: 'relevant_content_processor',
      data: {
        artifact: 'business_flows.txt',
        description: 'Identified business processes'
      }
    },
    {
      id: 'e1-4',
      source: 'document_processor',
      target: 'relevant_content_processor',
      data: {
        artifact: 'merged_output.json',
        description: 'Full document content for filtering'
      }
    },
    {
      id: 'e4-5',
      source: 'relevant_content_processor',
      target: 'path_finder',
      data: {
        artifact: 'screen_graph.json',
        description: 'Screen relationship graph'
      }
    },
    {
      id: 'e5-6',
      source: 'path_finder',
      target: 'path_to_csv',
      data: {
        artifact: 'screen_paths.json',
        description: 'Valid screen navigation paths'
      }
    },
    {
      id: 'e6-7',
      source: 'path_to_csv',
      target: 'gen_test_case',
      data: {
        artifact: 'test_data.csv',
        description: 'Flattened test data combinations'
      }
    },
    {
      id: 'e7-8',
      source: 'gen_test_case',
      target: 'test_case_evaluator',
      data: {
        artifact: 'test_cases.json',
        description: 'Generated test cases'
      }
    }
  ]
}

// Helper functions for pipeline configuration
export const getPipelineNodeById = (id: string): PipelineNode | undefined => {
  return MASTER_PIPELINE_CONFIG.nodes.find(node => node.id === id)
}

export const getPipelineEdgesBySource = (sourceId: string): PipelineEdge[] => {
  return MASTER_PIPELINE_CONFIG.edges.filter(edge => edge.source === sourceId)
}

export const getPipelineEdgesByTarget = (targetId: string): PipelineEdge[] => {
  return MASTER_PIPELINE_CONFIG.edges.filter(edge => edge.target === targetId)
}

export const getNodeDependencies = (nodeId: string): string[] => {
  return getPipelineEdgesByTarget(nodeId).map(edge => edge.source)
}

export const getNodeOutputs = (nodeId: string): string[] => {
  return getPipelineEdgesBySource(nodeId).map(edge => edge.target)
}
