"""
Terminal logger utility for sending logs to WebSocket terminal
"""
import asyncio
import logging
import sys
import io
import time
from typing import Op<PERSON>
from datetime import datetime
from .websocket_manager import websocket_manager

# Helper function to broadcast to live terminals
async def _broadcast_to_live_terminals(log_data: dict):
    """Broadcast log message to live terminals"""
    try:
        from ..api.v1.websocket import broadcast_to_live_terminals
        await broadcast_to_live_terminals(log_data)
    except Exception:
        pass  # Fail silently to avoid breaking the application

class TerminalLogger:
    def __init__(self, module_name: str = "system", run_id: str = "default"):
        self.module_name = module_name
        self.run_id = run_id
    
    def info(self, message: str):
        """Send info level log"""
        self._send_log(message, "info")
    
    def error(self, message: str):
        """Send error level log"""
        self._send_log(message, "error")
    
    def warn(self, message: str):
        """Send warning level log"""
        self._send_log(message, "warn")
    
    def debug(self, message: str):
        """Send debug level log"""
        self._send_log(message, "debug")
    
    def status(self, status: str, details: Optional[str] = None):
        """Send status update"""
        try:
            log_data = {
                "level": "info",
                "message": f"Status: {status}" + (f" - {details}" if details else ""),
                "step_id": self.module_name,
                "metadata": {"type": "status", "status": status, "details": details}
            }

            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Send to specific run_id connections only
                # (Live terminals are handled by WebSocketLogHandler from LogCapture)
                asyncio.create_task(websocket_manager.broadcast_log(
                    run_id=self.run_id,
                    log_data=log_data
                ))
            else:
                # Send to specific run_id connections only
                # (Live terminals are handled by WebSocketLogHandler from LogCapture)
                loop.run_until_complete(websocket_manager.broadcast_log(
                    run_id=self.run_id,
                    log_data=log_data
                ))
        except Exception:
            pass  # Fail silently if no event loop

    def pipeline_update(self, step: str, status: str, progress: Optional[int] = None):
        """Send pipeline execution update"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Use the correct method name: send_step_update with actual run_id
                asyncio.create_task(websocket_manager.send_step_update(self.run_id, step, status, progress))
            else:
                loop.run_until_complete(websocket_manager.send_step_update(self.run_id, step, status, progress))
        except Exception:
            pass  # Fail silently if no event loop
    
    def _send_log(self, message: str, level: str):
        """Internal method to send log message using standardized broadcast_log"""
        try:
            log_data = {
                "level": level,
                "message": message,
                "step_id": self.module_name
            }

            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Send to specific run_id connections only
                # (Live terminals are handled by WebSocketLogHandler from LogCapture)
                asyncio.create_task(websocket_manager.broadcast_log(
                    run_id=self.run_id,
                    log_data=log_data
                ))
            else:
                # Send to specific run_id connections only
                # (Live terminals are handled by WebSocketLogHandler from LogCapture)
                loop.run_until_complete(websocket_manager.broadcast_log(
                    run_id=self.run_id,
                    log_data=log_data
                ))
        except Exception:
            pass  # Fail silently if no event loop

# Global instance for easy import
terminal_logger = TerminalLogger()

# Factory function to create module-specific loggers
def get_terminal_logger(module_name: str, run_id: str = "default") -> TerminalLogger:
    return TerminalLogger(module_name, run_id)

class WebSocketLogHandler(logging.Handler):
    """Custom log handler that sends logs to WebSocket terminal with delay control"""

    def __init__(self, run_id: str, step_id: str, delay_seconds: float = 1.0):
        super().__init__()
        self.run_id = run_id
        self.step_id = step_id
        self.delay_seconds = delay_seconds
        self.last_log_time = 0
        self.setLevel(logging.INFO)

    def emit(self, record):
        """Send log record to WebSocket with delay control"""
        try:
            # Debug: Print module name to console to see what we're capturing
            print(f"[DEBUG] WebSocketLogHandler capturing log from module: '{record.name}' - Message: {record.getMessage()[:100]}...")

            # Apply delay to prevent log spam
            current_time = time.time()
            if current_time - self.last_log_time < self.delay_seconds:
                time.sleep(self.delay_seconds - (current_time - self.last_log_time))
            self.last_log_time = time.time()

            # Format the log message with timestamp
            message = self.format(record)
            timestamp = datetime.now().strftime("%H:%M:%S")

            # Add module name and level to message for better readability
            formatted_message = f"[{timestamp}] [{record.levelname}] [{record.name}] {message}"

            # Map Python log levels to our levels
            level_mapping = {
                'DEBUG': 'debug',
                'INFO': 'info',
                'WARNING': 'warn',
                'ERROR': 'error',
                'CRITICAL': 'error'
            }
            level = level_mapping.get(record.levelname, 'info')

            # Send to WebSocket asynchronously
            try:
                log_data = {
                    "level": level,
                    "message": formatted_message,
                    "step_id": self.step_id,
                    "module": record.name
                }

                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Only broadcast to live terminals to avoid duplicates
                    # (LogEmitter handles run_id specific connections)
                    asyncio.create_task(_broadcast_to_live_terminals(log_data))
                else:
                    # Only broadcast to live terminals to avoid duplicates
                    loop.run_until_complete(_broadcast_to_live_terminals(log_data))
            except Exception:
                pass  # Fail silently if no event loop
        except Exception:
            pass  # Fail silently to avoid breaking the application

class StdoutCapture:
    """Capture stdout/stderr and send to WebSocket"""

    def __init__(self, run_id: str, step_id: str, original_stream):
        self.run_id = run_id
        self.step_id = step_id
        self.original_stream = original_stream
        self.buffer = io.StringIO()

    def write(self, text):
        # Write to original stream
        self.original_stream.write(text)
        self.original_stream.flush()

        # Also send to WebSocket if it's meaningful content
        if text.strip():
            try:
                log_data = {
                    "level": "info",
                    "message": text.strip(),
                    "step_id": self.step_id,
                    "module": "stdout"
                }

                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Send to specific run_id connections only
                    # (Live terminals are handled by WebSocketLogHandler from LogCapture)
                    asyncio.create_task(websocket_manager.broadcast_log(
                        run_id=self.run_id,
                        log_data=log_data
                    ))
            except Exception:
                pass

    def flush(self):
        self.original_stream.flush()

def setup_module_logging(run_id: str, step_id: str, module_names: list = None, delay_seconds: float = 1.0):
    """
    Setup logging capture for specific modules to send logs to WebSocket terminal

    Args:
        run_id: Pipeline run ID
        step_id: Current step ID
        module_names: List of module names to capture logs from
        delay_seconds: Delay between log messages to prevent spam (default: 1.0 seconds)
    """
    if module_names is None:
        # Default modules to capture
        module_names = [
            'back_end.document_processor',
            'back_end.BusinessFlowDetector',
            'back_end.relevant_content_processor',
            'back_end.path_processor',
            'back_end.path_to_csv',
            'back_end.test_case_generator',
            'back_end.test_case_evaluator',
            '__main__'  # Capture main module logs
        ]

    # First, cleanup any existing WebSocket handlers to prevent duplicates
    cleanup_existing_websocket_handlers()

    # Create WebSocket handler with delay
    ws_handler = WebSocketLogHandler(run_id, step_id, delay_seconds)
    ws_handler.setFormatter(logging.Formatter('%(message)s'))

    # Add handler to specified modules
    print(f"[DEBUG] Setting up WebSocketLogHandler for modules: {module_names}")
    for module_name in module_names:
        logger = logging.getLogger(module_name)
        logger.addHandler(ws_handler)
        logger.setLevel(logging.INFO)
        print(f"[DEBUG] Added WebSocketLogHandler to module: '{module_name}'")

    # Also capture root logger for general logs
    root_logger = logging.getLogger()
    root_logger.addHandler(ws_handler)

    return ws_handler

def setup_stdout_capture(run_id: str, step_id: str):
    """Setup stdout/stderr capture to send to WebSocket terminal"""
    original_stdout = sys.stdout
    original_stderr = sys.stderr

    # Replace stdout and stderr with our capture objects
    sys.stdout = StdoutCapture(run_id, step_id, original_stdout)
    sys.stderr = StdoutCapture(run_id, step_id, original_stderr)

    return original_stdout, original_stderr

def restore_stdout_capture(original_stdout, original_stderr):
    """Restore original stdout/stderr"""
    sys.stdout = original_stdout
    sys.stderr = original_stderr

def cleanup_existing_websocket_handlers():
    """Remove all existing WebSocket handlers from all loggers to prevent duplicates"""
    # Remove from root logger
    root_logger = logging.getLogger()
    handlers_to_remove = [h for h in root_logger.handlers if isinstance(h, WebSocketLogHandler)]
    for handler in handlers_to_remove:
        root_logger.removeHandler(handler)

    # Remove from all other loggers
    for name in logging.Logger.manager.loggerDict:
        logger = logging.getLogger(name)
        handlers_to_remove = [h for h in logger.handlers if isinstance(h, WebSocketLogHandler)]
        for handler in handlers_to_remove:
            logger.removeHandler(handler)

def cleanup_module_logging(handler):
    """Remove the WebSocket handler from all loggers"""
    if handler:
        # Remove from root logger
        root_logger = logging.getLogger()
        root_logger.removeHandler(handler)

        # Remove from all other loggers that might have it
        for name in logging.Logger.manager.loggerDict:
            logger = logging.getLogger(name)
            if handler in logger.handlers:
                logger.removeHandler(handler)

class LogCapture:
    """Context manager for capturing module logs and stdout with delay control"""

    def __init__(self, run_id: str, step_id: str, module_names: list = None, delay_seconds: float = 1.0):
        self.run_id = run_id
        self.step_id = step_id
        self.module_names = module_names
        self.delay_seconds = delay_seconds
        self.log_handler = None
        self.original_stdout = None
        self.original_stderr = None

    def __enter__(self):
        """Setup log capture with delay"""
        self.log_handler = setup_module_logging(self.run_id, self.step_id, self.module_names, self.delay_seconds)
        self.original_stdout, self.original_stderr = setup_stdout_capture(self.run_id, self.step_id)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Cleanup log capture"""
        if self.original_stdout and self.original_stderr:
            restore_stdout_capture(self.original_stdout, self.original_stderr)
        if self.log_handler:
            cleanup_module_logging(self.log_handler)