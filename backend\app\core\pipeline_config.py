"""
Master Pipeline Configuration - Single Source of Truth for Backend Architecture
This configuration MUST exactly match the frontend pipelineConfig.ts
"""
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

# Master Pipeline Configuration - Definitive 8-Step Architecture
MASTER_PIPELINE_CONFIG = [
    {
        "id": "document_processor",
        "name": "Document Processor", 
        "description": "Processes PDF documents with interactive options and structured output",
        "script_path": "pipelines.real_pipelines.process_document",
        "function_name": "process_document",
        "inputs": ["input_document.pdf"],
        "outputs": ["document_structure.json", "merged_output.json", "analyzed_output.json"],
        "step_number": 1,
        "estimated_time": "30-60s",
        "complexity": "medium"
    },
    {
        "id": "extract_and_process_to_json", 
        "name": "Image & Diagram Extractor",
        "description": "Extracts images/diagrams from PDF, uses <PERSON> to analyze them into structured JSON",
        "script_path": "pipelines.real_pipelines.process_diagrams",
        "function_name": "process_diagrams", 
        "inputs": ["pdf_file"],
        "outputs": ["diagram_analysis.json", "extracted_images/"],
        "step_number": 2,
        "estimated_time": "45-90s", 
        "complexity": "high"
    },
    {
        "id": "business_flow_detector",
        "name": "Business Flow Detector",
        "description": "Generates business flows using the user's BusinessFlowDetector.py script", 
        "script_path": "pipelines.real_pipelines.generate_business_flows",
        "function_name": "generate_business_flows",
        "inputs": ["document_structure.json"],
        "outputs": ["business_flows/", "business_flow_summary.json"],
        "step_number": 3,
        "estimated_time": "60-120s",
        "complexity": "high"
    },
    {
        "id": "relevant_content_processor",
        "name": "Relevant Content Processor", 
        "description": "Filters and processes relevant content based on business flows",
        "script_path": "pipelines.real_pipelines.process_relevant_content",
        "function_name": "process_relevant_content",
        "inputs": ["merged_output.json", "business_flows/"],
        "outputs": ["relevant_content/", "screen_graph.json"],
        "step_number": 4,
        "estimated_time": "30-45s",
        "complexity": "medium"
    },
    {
        "id": "path_finder",
        "name": "Path Finder",
        "description": "Defines screen paths and finds valid paths using BFS algorithm",
        "script_path": "pipelines.real_pipelines.generate_screen_paths", 
        "function_name": "generate_screen_paths",
        "inputs": ["screen_graph.json"],
        "outputs": ["screen_paths.json", "path_analysis.json"],
        "step_number": 5,
        "estimated_time": "10-25s",
        "complexity": "medium",
        "sub_steps": [
            {
                "id": "path_processor",
                "name": "Path Processor",
                "description": "Defines screens, variables, and navigation rules"
            },
            {
                "id": "bfs_pathfinder", 
                "name": "BFS Pathfinder",
                "description": "Finds shortest valid paths between screens using BFS"
            }
        ]
    },
    {
        "id": "path_to_csv",
        "name": "Path to CSV Converter",
        "description": "Converts screen paths to CSV format for test case generation",
        "script_path": "pipelines.real_pipelines.convert_paths_to_csv",
        "function_name": "convert_paths_to_csv", 
        "inputs": ["screen_paths.json"],
        "outputs": ["test_paths.csv", "path_metadata.json"],
        "step_number": 6,
        "estimated_time": "5-10s",
        "complexity": "low"
    },
    {
        "id": "gen_test_case",
        "name": "Test Case Generator",
        "description": "Generates comprehensive test cases from processed paths and content",
        "script_path": "pipelines.real_pipelines.generate_test_cases",
        "function_name": "generate_test_cases",
        "inputs": ["test_paths.csv", "relevant_content/"],
        "outputs": ["test_cases/", "test_suite.json"],
        "step_number": 7,
        "estimated_time": "45-75s",
        "complexity": "high"
    },
    {
        "id": "test_case_evaluator",
        "name": "Test Case Evaluator", 
        "description": "Evaluates and validates generated test cases for quality and coverage",
        "script_path": "pipelines.real_pipelines.evaluate_test_cases",
        "function_name": "evaluate_test_cases",
        "inputs": ["test_cases/", "test_suite.json"],
        "outputs": ["evaluation_report.json", "quality_metrics.json"],
        "step_number": 8,
        "estimated_time": "20-30s",
        "complexity": "medium"
    }
]

def get_master_pipeline_config() -> List[Dict[str, Any]]:
    """
    Get the master pipeline configuration.
    
    Returns:
        List of pipeline module configurations
    """
    return MASTER_PIPELINE_CONFIG.copy()

def get_pipeline_module_by_id(module_id: str) -> Optional[Dict[str, Any]]:
    """
    Get a specific pipeline module by its ID.
    
    Args:
        module_id: The unique identifier for the pipeline module
        
    Returns:
        Pipeline module configuration or None if not found
    """
    for module in MASTER_PIPELINE_CONFIG:
        if module["id"] == module_id:
            return module.copy()
    return None

def get_pipeline_module_count() -> int:
    """
    Get the total number of pipeline modules.
    
    Returns:
        Total count of pipeline modules
    """
    return len(MASTER_PIPELINE_CONFIG)

def validate_frontend_sync() -> bool:
    """
    Validate that this configuration matches the frontend pipelineConfig.ts.
    
    Returns:
        True if configuration appears valid for frontend sync
    """
    required_ids = {
        "document_processor", "extract_and_process_to_json", "business_flow_detector",
        "relevant_content_processor", "path_finder", "path_to_csv", 
        "gen_test_case", "test_case_evaluator"
    }
    
    config_ids = {module["id"] for module in MASTER_PIPELINE_CONFIG}
    
    if config_ids == required_ids:
        logger.info("✅ Pipeline configuration IDs match expected frontend configuration")
        return True
    else:
        missing = required_ids - config_ids
        extra = config_ids - required_ids
        logger.error(f"❌ Pipeline configuration mismatch - Missing: {missing}, Extra: {extra}")
        return False

# Export for easy access
__all__ = [
    'MASTER_PIPELINE_CONFIG',
    'get_master_pipeline_config', 
    'get_pipeline_module_by_id',
    'get_pipeline_module_count',
    'validate_frontend_sync'
]
