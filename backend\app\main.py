"""
FastAPI main application for Python Interactive Execution Dashboard
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .api.v1.endpoints import router as api_router
from .api.v1.websocket import router as websocket_router
from .core.config import settings

# Import pipeline modules to populate the registry
try:
    import sys
    import os
    import logging

    # Add parent directory to path to import pipelines
    parent_dir = os.path.dirname(os.path.dirname(__file__))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    # Import master pipeline configuration and module loader
    from .core.pipeline_config import get_pipeline_module_count, validate_frontend_sync
    from .core.module_loader import load_pipeline_modules

    # Import all pipeline modules through the __init__.py (for decorator registration)
    import pipelines

    # Load and validate all pipeline modules
    load_results = load_pipeline_modules()

    # Get counts and status
    expected_count = get_pipeline_module_count()
    successful_loads = load_results["successful_loads"]
    failed_loads = load_results["failed_loads"]
    is_synced = validate_frontend_sync()

    # Determine success status
    load_success = (failed_loads == 0 and successful_loads == expected_count)

    # Log detailed startup information
    if load_success:
        logging.info(f"✅ All {expected_count} pipeline modules loaded successfully")
        print(f"✅ All {expected_count} pipeline modules loaded successfully")
    else:
        logging.warning(f"⚠️ Partial load: {successful_loads}/{expected_count} modules loaded, {failed_loads} failed")
        print(f"⚠️ Partial load: {successful_loads}/{expected_count} modules loaded, {failed_loads} failed")

    # Log validation results
    validation = load_results["validation"]
    logging.info(f"📋 Pipeline step registrations: {validation['total_registered']}")
    logging.info(f"🔄 Frontend sync status: {'✅ Synced' if is_synced else '❌ Out of sync'}")

    if not is_synced:
        print("⚠️ Warning: Pipeline configuration may be out of sync with frontend")

    if validation["missing_registrations"]:
        logging.warning(f"❌ Missing registrations: {validation['missing_registrations']}")
        print(f"❌ Missing registrations: {validation['missing_registrations']}")

except ImportError as e:
    print(f"⚠️ Warning: Could not import pipeline modules: {e}")
    print("Pipeline functionality may be limited")
    logging.error(f"Pipeline import failed: {e}")

app = FastAPI(
    title="Python Interactive Execution Dashboard",
    description="A web application for executing Python processing pipelines and visualizing DAGs",
    version="1.0.0"
)

# Cleanup any existing WebSocket handlers on startup to prevent duplicates
try:
    from .core.terminal_logger import cleanup_existing_websocket_handlers
    cleanup_existing_websocket_handlers()
    print("✅ Cleaned up existing WebSocket handlers")
except Exception as e:
    print(f"⚠️ Warning: Could not cleanup WebSocket handlers: {e}")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")
# Include WebSocket routes for live terminal
app.include_router(websocket_router, prefix="/api/v1/ws")

@app.get("/")
async def root():
    return {"message": "Python Interactive Execution Dashboard API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
