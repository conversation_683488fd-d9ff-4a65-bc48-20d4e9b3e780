"""
Pipeline step execution service
"""
import asyncio
import subprocess
import json
import os
import tempfile
from typing import Dict, Any, Optional
import logging
from datetime import datetime

from ..core.decorators import get_pipeline_step
from .session import session_manager

logger = logging.getLogger(__name__)

class PipelineExecutor:
    """Service for executing pipeline steps"""
    
    def __init__(self):
        self.running_processes: Dict[str, subprocess.Popen] = {}
    
    async def execute_step(
        self, 
        step_id: str, 
        parameters: Dict[str, Any],
        session_id: str = "default"
    ) -> Dict[str, Any]:
        """
        Execute a pipeline step with given parameters
        
        Args:
            step_id: ID of the step to execute
            parameters: Parameters to pass to the step
            session_id: Session ID for tracking
            
        Returns:
            Execution result with status and output information
        """
        step_info = get_pipeline_step(step_id)
        if not step_info:
            return {
                "success": False,
                "error": f"Step not found: {step_id}",
                "step_id": step_id
            }
        
        try:
            # Update step status to running
            await session_manager.update_step_status(session_id, step_id, "running")
            
            # Prepare execution environment
            execution_context = self._prepare_execution_context(step_id, parameters, session_id)
            
            # Execute the step function
            result = await self._execute_step_function(step_info, parameters, execution_context)
            
            # Update step status based on result
            status = "success" if result["success"] else "failed"
            await session_manager.update_step_status(session_id, step_id, status)
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing step {step_id}: {e}")
            await session_manager.update_step_status(session_id, step_id, "failed")
            return {
                "success": False,
                "error": str(e),
                "step_id": step_id
            }
    
    def _prepare_execution_context(
        self, 
        step_id: str, 
        parameters: Dict[str, Any],
        session_id: str
    ) -> Dict[str, Any]:
        """Prepare execution context for the step"""
        workspace_dir = session_manager.get_session_workspace(session_id)
        
        return {
            "step_id": step_id,
            "session_id": session_id,
            "workspace_dir": workspace_dir,
            "input_dir": os.path.join(workspace_dir, "input"),
            "output_dir": os.path.join(workspace_dir, "output"),
            "temp_dir": os.path.join(workspace_dir, "temp"),
            "parameters": parameters,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _execute_step_function(
        self,
        step_info: Dict[str, Any],
        parameters: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute the actual step function"""
        try:
            # Get the function
            func = step_info["function"]
            
            # Prepare function arguments
            func_kwargs = {}
            for param_name, param_info in step_info["parameters"].items():
                if param_name in parameters:
                    func_kwargs[param_name] = parameters[param_name]
                elif param_info.get("default") is not None:
                    func_kwargs[param_name] = param_info["default"]
            
            # Add context information
            func_kwargs["_context"] = context
            
            # Execute function in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, func, **func_kwargs)
            
            # Process result
            if isinstance(result, dict):
                return {
                    "success": True,
                    "result": result,
                    "step_id": step_info["id"],
                    "outputs": self._collect_outputs(context, step_info["outputs"])
                }
            else:
                return {
                    "success": True,
                    "result": {"output": result},
                    "step_id": step_info["id"],
                    "outputs": self._collect_outputs(context, step_info["outputs"])
                }
                
        except Exception as e:
            logger.error(f"Function execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "step_id": step_info["id"]
            }
    
    def _collect_outputs(self, context: Dict[str, Any], expected_outputs: list) -> Dict[str, Any]:
        """Collect output files generated by the step"""
        outputs = {}
        output_dir = context["output_dir"]
        
        if os.path.exists(output_dir):
            for output_name in expected_outputs:
                output_path = os.path.join(output_dir, output_name)
                if os.path.exists(output_path):
                    outputs[output_name] = {
                        "path": output_path,
                        "size": os.path.getsize(output_path),
                        "modified": os.path.getmtime(output_path)
                    }
        
        return outputs
    
    async def stop_execution(self, step_id: str, session_id: str = "default") -> bool:
        """Stop a running step execution"""
        process_key = f"{session_id}:{step_id}"
        if process_key in self.running_processes:
            try:
                process = self.running_processes[process_key]
                process.terminate()
                await asyncio.sleep(1)  # Give it time to terminate gracefully
                if process.poll() is None:
                    process.kill()  # Force kill if still running
                del self.running_processes[process_key]
                await session_manager.update_step_status(session_id, step_id, "failed")
                return True
            except Exception as e:
                logger.error(f"Error stopping execution {step_id}: {e}")
                return False
        return False

# Global instance
executor = PipelineExecutor()
