/**
 * PHOENIX PROJECT: Inspector Component
 * Read-only artifact viewer. When a node ID is selected, it fetches and displays results.
 * Rebuilt from the ground up with absolute precision.
 */
import React, { useEffect, useState } from 'react';
import { Eye, FileText, Download, AlertCircle } from 'lucide-react';
import { useSelectedNodeId, usePipelineRunId } from '../store/appStore';

interface Artifact {
  name: string;
  type: 'text' | 'binary' | 'error';
  content: string;
}

interface ArtifactsResponse {
  runId: string;
  stepId: string;
  artifacts: Artifact[];
  count: number;
}

export const Inspector: React.FC = () => {
  const selectedNodeId = useSelectedNodeId();
  const pipelineRunId = usePipelineRunId();
  
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedArtifact, setSelectedArtifact] = useState<Artifact | null>(null);

  // PHOENIX BLUEPRINT: Fetch artifacts when node is selected
  useEffect(() => {
    if (!selectedNodeId || !pipelineRunId) {
      setArtifacts([]);
      setSelectedArtifact(null);
      setError(null);
      return;
    }

    const fetchArtifacts = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/v1/artifacts/${pipelineRunId}/${selectedNodeId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setError('No artifacts found for this step');
          } else {
            throw new Error(`Failed to fetch artifacts: ${response.statusText}`);
          }
          return;
        }

        const data: ArtifactsResponse = await response.json();
        setArtifacts(data.artifacts);
        
        // Auto-select first artifact if available
        if (data.artifacts.length > 0) {
          setSelectedArtifact(data.artifacts[0]);
        }
        
      } catch (err) {
        console.error('Failed to fetch artifacts:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchArtifacts();
  }, [selectedNodeId, pipelineRunId]);

  // PHOENIX BLUEPRINT: Render artifact content
  const renderArtifactContent = (artifact: Artifact) => {
    if (artifact.type === 'error') {
      return (
        <div className="text-red-400 p-4 bg-red-900/20 rounded border border-red-700">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="w-4 h-4" />
            <span className="font-medium">Error</span>
          </div>
          <pre className="text-sm whitespace-pre-wrap">{artifact.content}</pre>
        </div>
      );
    }

    if (artifact.type === 'binary') {
      return (
        <div className="text-gray-400 p-4 bg-gray-800/50 rounded border border-gray-600">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="w-4 h-4" />
            <span className="font-medium">Binary File</span>
          </div>
          <p className="text-sm">{artifact.content}</p>
        </div>
      );
    }

    // Text content
    try {
      // Try to parse as JSON for pretty formatting
      const parsed = JSON.parse(artifact.content);
      return (
        <pre className="text-sm text-gray-300 whitespace-pre-wrap overflow-auto">
          {JSON.stringify(parsed, null, 2)}
        </pre>
      );
    } catch {
      // Not JSON, display as plain text
      return (
        <pre className="text-sm text-gray-300 whitespace-pre-wrap overflow-auto">
          {artifact.content}
        </pre>
      );
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-800">
      {/* PHOENIX BLUEPRINT: Inspector Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Eye className="w-5 h-5 text-gray-400" />
          <h2 className="text-xl font-bold text-white">Inspector</h2>
        </div>
        <p className="text-gray-400 text-sm">
          {selectedNodeId 
            ? `Viewing artifacts for: ${selectedNodeId}` 
            : 'Select a completed pipeline step to view artifacts'
          }
        </p>
      </div>

      {/* PHOENIX BLUEPRINT: Content Area */}
      <div className="flex-1 flex">
        {!selectedNodeId || !pipelineRunId ? (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Select a completed pipeline step to view its artifacts</p>
            </div>
          </div>
        ) : loading ? (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p>Loading artifacts...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex-1 flex items-center justify-center text-red-400">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 mx-auto mb-4" />
              <p>{error}</p>
            </div>
          </div>
        ) : artifacts.length === 0 ? (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No artifacts found for this step</p>
            </div>
          </div>
        ) : (
          <>
            {/* Artifact List */}
            <div className="w-1/3 border-r border-gray-700 p-4">
              <h3 className="text-white font-medium mb-3">Artifacts ({artifacts.length})</h3>
              <div className="space-y-2">
                {artifacts.map((artifact, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedArtifact(artifact)}
                    className={`
                      w-full text-left p-3 rounded border transition-colors
                      ${selectedArtifact === artifact
                        ? 'border-blue-500 bg-blue-900/20 text-blue-300'
                        : 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500'
                      }
                    `}
                  >
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      <span className="text-sm font-medium truncate">{artifact.name}</span>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {artifact.type} • {artifact.content.length} chars
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Artifact Content */}
            <div className="flex-1 p-4">
              {selectedArtifact ? (
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-white font-medium">{selectedArtifact.name}</h3>
                    <button
                      onClick={() => {
                        const blob = new Blob([selectedArtifact.content], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = selectedArtifact.name;
                        a.click();
                        URL.revokeObjectURL(url);
                      }}
                      className="flex items-center gap-2 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
                    >
                      <Download className="w-4 h-4" />
                      Download
                    </button>
                  </div>
                  
                  <div className="flex-1 bg-gray-900 rounded border border-gray-600 p-4 overflow-auto">
                    {renderArtifactContent(selectedArtifact)}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <p>Select an artifact to view its content</p>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};
