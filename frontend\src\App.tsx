/**
 * PHOENIX PROJECT: Main Application Component
 * Orchestrates the entire layout and manages overall application state.
 * Rebuilt from the ground up with absolute precision.
 */
import React from "react";
import { PipelineNavigator } from "./components/PipelineNavigator";
import { Inspector } from "./components/Inspector";
import { Header } from "./components/Header";
import { LiveTerminal } from "./components/LiveTerminal";
import { useCurrentFile, useGlobalStats } from "./store/appStore";

function App() {
    const currentFile = useCurrentFile();
    const globalStats = useGlobalStats();

    return (
        <div className="min-h-screen bg-gray-900 text-white">
            {/* PHOENIX BLUEPRINT: Header with Global Stats */}
            <Header
                currentFile={currentFile}
                tokens={globalStats.tokens}
                cost={globalStats.cost}
            />

            <div className="flex h-[calc(100vh-4rem)]">
                {/* PHOENIX BLUEPRINT: Left Panel - Pipeline Navigator */}
                <div className="w-1/2 border-r border-gray-700">
                    <PipelineNavigator />
                </div>

                {/* PHOENIX BLUEPRINT: Right Panel - Inspector and Terminal */}
                <div className="w-1/2 h-full flex flex-col">
                    {/* Inspector - Top Panel */}
                    <div className="h-1/2 border-b border-gray-700">
                        <Inspector />
                    </div>

                    {/* Terminal - Bottom Panel */}
                    <div className="h-1/2">
                        <LiveTerminal className="h-full w-full" />
                    </div>
                </div>
            </div>
        </div>
    );
}

export default App;
