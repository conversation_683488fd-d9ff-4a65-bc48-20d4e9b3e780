import os
import json
import re
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import google.generativeai as generativeai
from google import genai
from google.genai import types
from .rotate_api_key import APIKeyRotator
from .prompt_storing import (relevant_content_prompt, gen_screen_graph_prompt)
from .json_repair_utility import JSONRepairUtility
from .count_gemini_token import GeminiCostCalculator
import rapidfuzz as fuzz
from typing import Dict, Any

logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")

class RelevantContentProcessor:
    def __init__(
        self,
        description_json: str,
        merged_json: str,
        config_file: str,
        business_flows_dir: str,
        base_output_dir: str,
        rotate_api_key: bool = False,
        count_token: bool = False,
        max_workers: int = 3,
        enable_caching: bool = True
    ) -> None:
        """
        Khởi tạo với các đường dẫn file JSON và cấu hình.
        
        Args:
            description_json: Path to description JSON file
            merged_json: Path to merged JSON file
            config_file: Path to Gemini config file
            business_flows_dir: Directory containing Business Flow X.txt files
            base_output_dir: Base directory for output files
            rotate_api_key: Whether to rotate API keys
            count_token: Enable token counting and cost calculation
            max_workers: Maximum number of worker threads for parallel processing
            enable_caching: Enable/disable caching for relevant content
        """
        self.description_json = description_json
        self.merged_json = merged_json
        self.config_file = config_file
        self.business_flows_dir = business_flows_dir
        self.base_output_dir = base_output_dir
        self.count_token = count_token
        self.max_workers = max_workers
        self.enable_caching = enable_caching
        
        # Initialize token tracking
        self.cost_calculator = GeminiCostCalculator() if count_token else None
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.cache_tokens = 0  # Track cache tokens
        self.cache_created = False  # Track if cache was created
        self.total_cost = 0.0
        self.model_type = None  # Will be set based on config
        self.api_call_count = 0
        
        # Threading locks for thread-safe operations
        self.token_lock = threading.Lock()
        self.api_lock = threading.Lock()
        self.screen_graph_lock = threading.Lock()
        self.screen_graph_generated = False
        
        self.model = None
        self.client = None
        self.cache = None  # Cache object for storing relevant content

        # Tải cấu hình Gemini
        self.config = self._load_config()
        if self.config:
            # Determine model type for token calculation
            if self.count_token:
                self._determine_model_type()
                
            self.rotator = APIKeyRotator(config_path=self.config_file)
            # Chỉ xoay API key nếu rotate_api_key=True
            if rotate_api_key:
                self._rotate_and_configure_api_key()
            else:
                self._configure_api_key()
        else:
            logging.error("Failed to load Gemini configuration.")

        # Tải dữ liệu JSON từ description_json
        self.json_data = self._load_json(self.description_json)
        self.context = json.dumps(self.json_data, indent=4) if self.json_data else ""

        # Tải dữ liệu JSON từ merged_json cho screen graph
        self.merged_data = self._load_json(self.merged_json)
        self.merged_data_string = json.dumps(self.merged_data, indent=4, ensure_ascii=False) if self.merged_data else ""

    def _determine_model_type(self) -> None:
        """Xác định loại model (pro hay flash) dựa trên config file."""
        if not self.config:
            return
            
        model_name = self.config.get("config_gemini_model", {}).get("model_name", "").lower()
        
        if "flash" in model_name:
            self.model_type = "flash"
        elif "pro" in model_name:
            self.model_type = "pro"
        else:
            # Default to pro if unclear
            self.model_type = "pro"
            
        if self.count_token:
            logging.info(f"Token counting enabled for model type: {self.model_type}")

    def _calculate_token_cost(self, input_tokens: int, output_tokens: int) -> Dict[str, Any]:
        """
        Tính toán chi phí token dựa trên model type.
        
        Args:
            input_tokens: Số token đầu vào
            output_tokens: Số token đầu ra
            
        Returns:
            Dict chứa thông tin chi phí
        """
        if not self.cost_calculator or not self.model_type:
            return {}
            
        if self.model_type == "flash":
            return self.cost_calculator.calculate_flash_cost(input_tokens, output_tokens)
        else:
            return self.cost_calculator.calculate_pro_cost(input_tokens, output_tokens)

    def get_token_summary(self) -> Dict[str, Any]:
        """
        Trả về tổng kết chi phí token bao gồm cả cache costs.
        
        Returns:
            Dict chứa thông tin tổng kết về token và chi phí
        """
        if not self.count_token or not self.cost_calculator:
            return {"token_counting_enabled": False}
            
        # Calculate combined cost including cache
        if self.cache_created and self.cache_tokens > 0:
            combined_cost_info = self.cost_calculator.calculate_combined_cost_with_cache(
                model_type=self.model_type,
                input_tokens=self.total_input_tokens,
                output_tokens=self.total_output_tokens,
                cache_tokens=self.cache_tokens,
                storage_hours=1.0  # Default 1 hour storage
            )
            
            return {
                "token_counting_enabled": True,
                "model_type": self.model_type,
                "total_input_tokens": self.total_input_tokens,
                "total_output_tokens": self.total_output_tokens,
                "total_tokens": self.total_input_tokens + self.total_output_tokens,
                "api_call_count": self.api_call_count,
                "cache_enabled": True,
                "cache_tokens": self.cache_tokens,
                **combined_cost_info
            }
        else:
            # No cache, just regular API costs
            api_cost_info = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens)
            
            return {
                "token_counting_enabled": True,
                "model_type": self.model_type,
                "total_input_tokens": self.total_input_tokens,
                "total_output_tokens": self.total_output_tokens,
                "total_tokens": self.total_input_tokens + self.total_output_tokens,
                "api_call_count": self.api_call_count,
                "cache_enabled": False,
                **api_cost_info
            }

    def print_token_summary(self) -> None:
        """In tổng kết chi phí token và cost bao gồm cả cache costs."""
        if not self.count_token or not self.cost_calculator:
            return
            
        token_summary = self.get_token_summary()
        self.cost_calculator.print_token_summary(token_summary, "RELEVANT CONTENT")
        
        # Log billing information
        from .billing_logger import billing_logger
        billing_logger.log_module_cost("RelevantContentProcessor", token_summary)

    def _load_config(self) -> dict:
        """Tải cấu hình từ file JSON."""
        try:
            with open(self.config_file, "r", encoding="utf-8") as file:
                return json.load(file)
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return {}

    def _configure_api_key(self) -> None:
        """Cấu hình API key mà không xoay."""
        try:
            api_key = self.rotator.get_api_key()
            generativeai.configure(api_key=api_key)
            self.model = generativeai.GenerativeModel(
                self.config.get("config_gemini_model", {}).get("model_name", "gemini-1.5-pro"),
                generation_config={"response_mime_type": self.config.get("config_gemini_model", {}).get("response_mime_type", "application/json")}
            )
            self.client = genai.Client(api_key=api_key)
            logging.info("Configured Gemini model with current API key.")
        except Exception as e:
            logging.error(f"Error configuring API key: {e}")
            self.model = None
            self.client = None
    
    def _ensure_output_directories(self, *paths) -> None:
        """Đảm bảo rằng các thư mục output tồn tại."""
        output_dirs = set(os.path.dirname(path) for path in paths if path)
        for output_dir in output_dirs:
            if output_dir and not os.path.exists(output_dir):  # Kiểm tra nếu thư mục không rỗng và chưa tồn tại
                os.makedirs(output_dir, exist_ok=True)
                logging.info(f"Created output directory: {output_dir}")

    def _get_business_flow_files(self) -> list:
        """Tìm tất cả các file Business Flow X.txt trong thư mục."""
        business_flow_files = []
        if not os.path.exists(self.business_flows_dir):
            logging.error(f"Business flows directory not found: {self.business_flows_dir}")
            return business_flow_files
        
        # Pattern để match file Business Flow X.txt
        pattern = re.compile(r'^Business Flow (\d+)\.txt$')
        
        for filename in os.listdir(self.business_flows_dir):
            match = pattern.match(filename)
            if match:
                flow_number = int(match.group(1))
                file_path = os.path.join(self.business_flows_dir, filename)
                business_flow_files.append({
                    'number': flow_number,
                    'filename': filename,
                    'path': file_path
                })
        
        # Sắp xếp theo số thứ tự
        business_flow_files.sort(key=lambda x: x['number'])
        logging.info(f"Found {len(business_flow_files)} business flow files")
        return business_flow_files

    def _get_output_paths_for_flow(self, flow_number: int) -> dict:
        """Tạo các đường dẫn output cho một business flow cụ thể."""
        flow_dir = os.path.join(self.base_output_dir, f"business_flow_{flow_number}")
        return {
            'flow_dir': flow_dir,
            'relevant_json': os.path.join(flow_dir, 'relevant_content.json'),
            'output_json': os.path.join(flow_dir, 'merged_relevant_content.json')
        }

    def _get_shared_screen_graph_path(self) -> str:
        """Trả về đường dẫn cho screen graph chung."""
        return os.path.join(self.base_output_dir, 'screen_graph.json')

    def _rotate_and_configure_api_key(self) -> None:
        """Xoay và cấu hình API key."""
        try:
            api_key = self.rotator.get_api_key()
            self.rotator.rotate_api_key()
            generativeai.configure(api_key=api_key)
            self.model = generativeai.GenerativeModel(
                self.config.get("config_gemini_model", {}).get("model_name", "gemini-1.5-pro"),
                generation_config={"response_mime_type": self.config.get("config_gemini_model", {}).get("response_mime_type", "application/json")}
            )
            logging.info("Rotated and configured Gemini model with new API key.")
        except Exception as e:
            logging.error(f"Error rotating and configuring API key: {e}")
            self.model = None

    def _load_json(self, filepath: str) -> list:
        """Tải dữ liệu từ file JSON."""
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logging.error(f"Error loading JSON from {filepath}: {e}")
            return []

    def generate_relevant_content(self, business_process: str, relevant_json_path: str, rotate_api_key: bool = False) -> str:
        """Sinh nội dung liên quan từ Gemini dựa trên truy vấn và context."""
        if rotate_api_key:
            self._rotate_and_configure_api_key()
        elif not self.model:
            self._configure_api_key()

        if not self.json_data or not self.model:
            logging.error("Invalid JSON data or Gemini model not initialized.")
            return ""

        try:
            prompt = f"""
            {relevant_content_prompt}\n

            **User Query:**\n 
            {business_process}\n

            **JSON Description:**\n 
            {self.context}\n
            """
            
            # Count input tokens if enabled
            input_tokens = 0
            if self.count_token:
                try:
                    input_token_count = self.model.count_tokens(prompt)
                    input_tokens = input_token_count.total_tokens
                    logging.info(f"Relevant Content - Input tokens: {input_tokens:,}")
                except Exception as e:
                    logging.warning(f"Failed to count input tokens: {e}")

            response = self.model.generate_content(prompt)
            logging.info("Received Gemini response for relevant content.")

            # Count output tokens if enabled
            output_tokens = 0
            if self.count_token:
                try:
                    if hasattr(response, 'usage_metadata') and response.usage_metadata:
                        output_tokens = response.usage_metadata.candidates_token_count
                    else:
                        # Fallback: estimate tokens based on response text
                        output_token_count = self.model.count_tokens(response.text)
                        output_tokens = output_token_count.total_tokens
                except Exception as e:
                    logging.warning(f"Failed to count output tokens: {e}")
            
            # Track tokens if enabled and successful (thread-safe)
            if self.count_token and input_tokens > 0:
                with self.token_lock:
                    self.total_input_tokens += input_tokens
                    self.total_output_tokens += output_tokens
                    self.api_call_count += 1
                
                # Calculate cost for this request
                cost_info = self._calculate_token_cost(input_tokens, output_tokens)
                
                logging.info(f"Relevant Content - Token usage - Input: {input_tokens:,}, Output: {output_tokens:,}")
                if cost_info:
                    logging.info(f"Relevant Content - Request cost: ${cost_info.get('total_cost_usd', 0):.6f}")
                    with self.token_lock:
                        cumulative_cost = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens).get('total_cost_usd', 0)
                    logging.info(f"Relevant Content - Cumulative cost: ${cumulative_cost:.6f}")

            # Use JSONRepairUtility to extract and repair JSON from response
            extracted_text = JSONRepairUtility.extract_json_from_text(response.text)
            
            # Verify and repair if needed
            try:
                json.loads(extracted_text)  # Check if valid JSON
            except json.JSONDecodeError:
                logging.warning("Initial JSON extraction invalid, attempting repair")
                extracted_text = JSONRepairUtility.repair_json(extracted_text)
            
            with open(relevant_json_path, "w", encoding="utf-8") as outfile:
                outfile.write(extracted_text)
            logging.info(f"Generated content saved to {relevant_json_path}")
            return extracted_text
        except Exception as e:
            logging.error(f"Gemini API Error in generate_relevant_content: {e}")
            return ""

    def _extract_flow_number_from_path(self, output_path: str) -> int:
        """Trích xuất flow number từ đường dẫn output."""
        try:
            # Tìm pattern business_flow_X trong đường dẫn
            match = re.search(r'business_flow_(\d+)', output_path)
            if match:
                return int(match.group(1))
        except Exception as e:
            logging.warning(f"Could not extract flow number from path {output_path}: {e}")
        return None

    def _create_cache(self, content: str, flow_number: int = None) -> bool:
        """Tạo cache cho nội dung với tên theo business process."""
        if not self.enable_caching:
            logging.info("Caching is disabled, skipping cache creation")
            return False

        if not self.client:
            logging.warning("Client not initialized, cannot create cache")
            return False

        # Tạo display name theo pattern
        if flow_number is not None:
            display_name = f"relevant_info_BP_{flow_number}"
        else:
            display_name = "relevant_info_general"

        # Check if cache already exists
        existing_cache = self._find_cache_by_display_name(display_name)
        if existing_cache:
            print(f"✅ Cache '{display_name}' already exists, skipping creation")
            self.cache = existing_cache
            with self.token_lock:
                self.cache_created = False  # We didn't create it, just found it
            return True

        try:
            # Count cache tokens if token counting is enabled (thread-safe)
            if self.count_token and self.model:
                try:
                    cache_token_count = self.model.count_tokens(content)
                    with self.token_lock:
                        self.cache_tokens = cache_token_count.total_tokens
                    logging.info(f"Cache tokens: {self.cache_tokens:,}")
                    
                    # Calculate and log cache cost
                    if self.cost_calculator and self.model_type:
                        if self.model_type == "flash":
                            cache_cost_info = self.cost_calculator.calculate_flash_cache_cost(self.cache_tokens)
                        else:
                            cache_cost_info = self.cost_calculator.calculate_pro_cache_cost(self.cache_tokens)
                        
                        logging.info(f"Cache cost: ${cache_cost_info.get('total_cache_cost_usd', 0):.6f}")
                except Exception as e:
                    logging.warning(f"Failed to count cache tokens: {e}")
            
            self.cache = self.client.caches.create(
                model=self.config.get("config_gemini_model", {}).get("model_name", "gemini-2.5-pro"),
                config=types.CreateCachedContentConfig(
                    system_instruction="Load all content into cache, do not replace or modify anything, keep the original content intact, including formatting",
                    contents=content,
                    display_name=display_name
                )
            )
            with self.token_lock:
                self.cache_created = True
            logging.info(f"Cache created successfully with name: {display_name}")
            print(f"🆕 New cache created with display name: {display_name}")
            return True
        except Exception as e:
            logging.error(f"Error creating cache: {e}")
            return False

    def merge_relevant_files(self, relevant_json_path: str, output_json_path: str) -> None:
        """Gộp nội dung file dựa trên danh sách tên file từ relevant_json với hybrid matching và caching."""
        relevant_data = self._load_json(relevant_json_path)
        merged_data = self._load_json(self.merged_json)

        if not relevant_data or not merged_data:
            logging.error("Failed to load relevant or merged JSON data.")
            return
        if not merged_data or not isinstance(merged_data, list) and not isinstance(merged_data, dict):
            logging.error("Failed when creating")
            return

        # Note: Local file caching đã được loại bỏ, chỉ sử dụng API caching
            
        # Trích xuất danh sách tên file từ relevant_json
        filenames_to_keep = [item["File_name"] for item in relevant_data]
        
        # Kiểm tra xem merged_data có phải là dict không và lấy list các items
        items_to_process = []
        if isinstance(merged_data, dict):
            # Giả sử dict chứa các list dưới dạng value, ta sẽ gộp chúng lại
            for key, value in merged_data.items():
                if isinstance(value, list):
                    items_to_process.extend(value)
                else:
                    logging.warning(f"Value for key '{key}' in merged_data is not a list and will be skipped.")
        elif isinstance(merged_data, list):
            items_to_process = merged_data
        else:
            logging.error("merged_data is not a list or a dictionary.")
            return

        # Hybrid matching approach: exact match first, then fuzzy
        results = []
        processed_items = set()  # Track items already processed
        similarity_threshold = 80  # Threshold for fuzzy matches
        
        # Step 1: Try exact matching first
        for item in items_to_process:
            if not isinstance(item, dict) or "title" not in item:
                continue
                
            item_title = item.get("title", "")
            
            # Check for exact match
            if item_title in filenames_to_keep:
                item["match_type"] = "exact"
                item["matched_with"] = item_title
                results.append(item)
                processed_items.add(id(item))  # Add item to processed set
                logging.info(f"Exact match: '{item_title}'")
        
        # Step 2: For unmatched files, try fuzzy matching
        # First, get all items that weren't exactly matched
        unmatched_items = [item for item in items_to_process 
                          if isinstance(item, dict) and "title" in item and id(item) not in processed_items]
        
        # Get filenames that weren't matched
        matched_titles = [item.get("matched_with") for item in results]
        unmatched_filenames = [name for name in filenames_to_keep if name not in matched_titles]
        
        # If we still have unmatched items and filenames, try fuzzy matching
        if unmatched_items and unmatched_filenames:
            logging.info(f"Trying fuzzy matching for {len(unmatched_filenames)} remaining filenames")
            
            for filename in unmatched_filenames:
                best_item = None
                best_score = 0
                
                for item in unmatched_items:
                    if id(item) in processed_items:
                        continue
                        
                    item_title = item.get("title", "")
                    # Use fuzzy matching
                    score = fuzz.fuzz.ratio(item_title, filename) * 100
                    
                    if score > best_score and score >= similarity_threshold:
                        best_score = score
                        best_item = item
                
                if best_item:
                    best_item["match_type"] = "fuzzy"
                    best_item["match_score"] = best_score
                    best_item["matched_with"] = filename
                    results.append(best_item)
                    processed_items.add(id(best_item))
                    logging.info(f"Fuzzy match: '{best_item.get('title')}' with '{filename}' (score: {best_score:.2f}%)")
        
        # Tạo cache cho kết quả nếu được bật
        if self.enable_caching:
            content_for_cache = json.dumps(results, ensure_ascii=False, indent=2)
            # Extract flow number from output path if possible
            flow_number = self._extract_flow_number_from_path(output_json_path)
            self._create_cache(content_for_cache, flow_number)
        else:
            logging.info("Caching disabled, skipping cache creation for merged results")
        
        # Note: Local file caching đã được loại bỏ
        
        # Ghi kết quả ra file output_json
        try:
            with open(output_json_path, "w", encoding="utf-8") as out:
                json.dump(results, out, ensure_ascii=False, indent=2)
            logging.info(f"JSON file created at {output_json_path} with {len(results)} matched items")
            logging.info(f"Exact matches: {len([i for i in results if i.get('match_type') == 'exact'])}")
            logging.info(f"Fuzzy matches: {len([i for i in results if i.get('match_type') == 'fuzzy'])}")
        except Exception as e:
            logging.error(f"Error writing output file {output_json_path}: {e}")

    def generate_screen_graph(self, screen_graph_json_path: str, rotate_api_key: bool = False) -> None:
        """Sinh thông tin đồ thị màn hình dạng cây cho thuật toán BFS."""
        if rotate_api_key:
            self._rotate_and_configure_api_key()
        elif not self.model:
            self._configure_api_key()

        if not self.merged_data or not self.model:
            logging.error("Invalid merged data or Gemini model not initialized.")
            return

        # Find and use cache with display name "merged_output"
        merged_output_cache = self._find_cache_by_display_name("merged_output")

        if merged_output_cache:
            print("🔄 Using cache 'merged_output' for screen graph generation...")
            prompt = f"{gen_screen_graph_prompt}\n"
        else:
            print("⚠️ Cache 'merged_output' not found, using merged_data_string as fallback...")
            prompt = f"""
                {gen_screen_graph_prompt}
            """
        
        try:
            # Count input tokens if enabled
            input_tokens = 0
            if self.count_token:
                try:
                    input_token_count = self.model.count_tokens(prompt)
                    input_tokens = input_token_count.total_tokens
                    logging.info(f"Screen Graph - Input tokens: {input_tokens:,}")
                except Exception as e:
                    logging.warning(f"Failed to count input tokens: {e}")

            # Generate content using cache if available
            if merged_output_cache:
                response = self.client.models.generate_content(
                    model="gemini-2.5-pro",
                    contents=prompt,
                    config=types.GenerateContentConfig(
                        cached_content=merged_output_cache.name
                    )
                )
            else:
                response = self.model.generate_content(prompt)
            logging.info("Received Gemini response for screen graph.")

            # Count output tokens if enabled
            output_tokens = 0
            if self.count_token:
                try:
                    if hasattr(response, 'usage_metadata') and response.usage_metadata:
                        output_tokens = response.usage_metadata.candidates_token_count
                    else:
                        # Fallback: estimate tokens based on response text
                        output_token_count = self.model.count_tokens(response.text)
                        output_tokens = output_token_count.total_tokens
                except Exception as e:
                    logging.warning(f"Failed to count output tokens: {e}")
            
            # Track tokens if enabled and successful (thread-safe)
            if self.count_token and input_tokens > 0:
                with self.token_lock:
                    self.total_input_tokens += input_tokens
                    self.total_output_tokens += output_tokens
                    self.api_call_count += 1
                
                # Calculate cost for this request
                cost_info = self._calculate_token_cost(input_tokens, output_tokens)
                
                logging.info(f"Screen Graph - Token usage - Input: {input_tokens:,}, Output: {output_tokens:,}")
                if cost_info:
                    logging.info(f"Screen Graph - Request cost: ${cost_info.get('total_cost_usd', 0):.6f}")
                    with self.token_lock:
                        cumulative_cost = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens).get('total_cost_usd', 0)
                    logging.info(f"Screen Graph - Cumulative cost: ${cumulative_cost:.6f}")

            # Use JSONRepairUtility to extract and repair JSON from response
            extracted_text = JSONRepairUtility.extract_json_from_text(response.text)
            
            # Verify and repair if needed
            try:
                json_data = json.loads(extracted_text)  # Check if valid JSON
                
                # Validate and fix the structure of the generated JSON for BFS algorithm
                json_data = JSONRepairUtility.validate_screen_graph_structure(json_data)
                extracted_text = json.dumps(json_data, indent=2, ensure_ascii=False)
                
            except json.JSONDecodeError:
                logging.warning("Initial JSON extraction invalid, attempting repair")
                extracted_text = JSONRepairUtility.repair_json(extracted_text)
                
                # Try to ensure the repaired JSON has the right structure
                try:
                    json_data = json.loads(extracted_text)
                    json_data = JSONRepairUtility.validate_screen_graph_structure(json_data)
                    extracted_text = json.dumps(json_data, indent=2, ensure_ascii=False)
                except:
                    # If all attempts fail, create a minimal valid structure
                    json_data = {"graph": {"nodes": [], "edges": []}}
                    extracted_text = json.dumps(json_data, indent=2, ensure_ascii=False)
            
            with open(screen_graph_json_path, "w", encoding="utf-8") as outfile:
                outfile.write(extracted_text)
            logging.info(f"Screen graph saved to {screen_graph_json_path}")
            
            # Log a summary of the graph
            try:
                json_data = json.loads(extracted_text)
                node_count = len(json_data.get("graph", {}).get("nodes", []))
                edge_count = len(json_data.get("graph", {}).get("edges", []))
                logging.info(f"Generated screen graph with {node_count} nodes and {edge_count} edges")
            except:
                logging.warning("Could not generate summary statistics for screen graph")
                
        except Exception as e:
            logging.error(f"Gemini API Error in generate_screen_graph: {e}")

    def generate_shared_screen_graph(self, rotate_api_key: bool = False) -> bool:
        """Sinh screen graph chung chỉ 1 lần cho tất cả business flows."""
        with self.screen_graph_lock:
            # Kiểm tra xem đã generate chưa
            screen_graph_path = self._get_shared_screen_graph_path()
            
            if self.screen_graph_generated or os.path.exists(screen_graph_path):
                logging.info("Screen graph already generated, skipping")
                return True
            
            # Tạo thư mục nếu chưa có
            self._ensure_output_directories(screen_graph_path)
            
            logging.info("Generating shared screen graph...")
            try:
                self.generate_screen_graph(screen_graph_path, rotate_api_key)
                self.screen_graph_generated = True
                logging.info(f"Shared screen graph generated successfully at: {screen_graph_path}")
                return True
            except Exception as e:
                logging.error(f"Failed to generate shared screen graph: {e}")
                return False

    def execute_single_flow(self, business_process: str, flow_number: int, rotate_api_key: bool = False) -> bool:
        """Thực hiện quy trình cho một business flow cụ thể (không bao gồm screen graph)."""
        output_paths = self._get_output_paths_for_flow(flow_number)
        
        # Tạo thư mục output
        self._ensure_output_directories(
            output_paths['relevant_json'],
            output_paths['output_json']
        )
        
        logging.info(f"Processing Business Flow {flow_number}")
        
        if self.generate_relevant_content(business_process, output_paths['relevant_json'], rotate_api_key=rotate_api_key):
            self.merge_relevant_files(output_paths['relevant_json'], output_paths['output_json'])
            logging.info(f"Successfully processed Business Flow {flow_number}")
            return True
        else:
            logging.error(f"Failed to generate relevant content for Business Flow {flow_number}")
            return False

    def _process_flow_worker(self, flow_info: dict, rotate_api_key: bool = False) -> dict:
        """Worker function để xử lý business flow trong thread."""
        flow_number = flow_info['number']
        flow_path = flow_info['path']
        
        result = {
            'flow_number': flow_number,
            'success': False,
            'error': None
        }
        
        try:
            # Đọc nội dung business flow
            with open(flow_path, 'r', encoding='utf-8') as f:
                business_process = f.read().strip()
            
            # Xử lý business flow
            result['success'] = self.execute_single_flow(business_process, flow_number, rotate_api_key)
            
        except Exception as e:
            result['error'] = str(e)
            logging.error(f"Error processing Business Flow {flow_number}: {e}")
        
        return result

    def execute_all_flows(self, rotate_api_key: bool = False, use_threading: bool = True) -> None:
        """Thực hiện toàn bộ quy trình cho tất cả business flows với multithreading."""
        business_flow_files = self._get_business_flow_files()
        
        if not business_flow_files:
            logging.error("No business flow files found")
            return
        
        # Tạo screen graph chung trước khi xử lý các business flows
        logging.info("Generating shared screen graph before processing business flows...")
        if not self.generate_shared_screen_graph(rotate_api_key):
            logging.warning("Failed to generate shared screen graph, but continuing with business flows")
        
        if not use_threading or len(business_flow_files) == 1:
            # Fallback to sequential processing
            self._execute_all_flows_sequential(business_flow_files, rotate_api_key)
            return
        
        logging.info(f"Starting parallel processing of {len(business_flow_files)} business flows with {self.max_workers} workers")
        
        successful_flows = 0
        failed_flows = 0
        results = []
        
        # Sử dụng ThreadPoolExecutor cho xử lý song song
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_flow = {
                executor.submit(self._process_flow_worker, flow_info, rotate_api_key): flow_info['number'] 
                for flow_info in business_flow_files
            }
            
            # Process completed tasks
            for future in as_completed(future_to_flow):
                flow_number = future_to_flow[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result['success']:
                        successful_flows += 1
                        logging.info(f"✓ Business Flow {result['flow_number']} completed successfully")
                    else:
                        failed_flows += 1
                        error_msg = result.get('error', 'Unknown error')
                        logging.error(f"✗ Business Flow {result['flow_number']} failed: {error_msg}")
                        
                except Exception as e:
                    failed_flows += 1
                    logging.error(f"✗ Business Flow {flow_number} failed with exception: {e}")
        
        # Sort results by flow number for better logging
        results.sort(key=lambda x: x['flow_number'])
        
        logging.info(f"Parallel processing completed: {successful_flows} successful, {failed_flows} failed")
        logging.info("Processing summary:")
        for result in results:
            status = "✓ SUCCESS" if result['success'] else f"✗ FAILED ({result.get('error', 'Unknown error')})"
            logging.info(f"  Business Flow {result['flow_number']}: {status}")
        
        # Print token summary if enabled
        if self.count_token:
            self.print_token_summary()

    def _execute_all_flows_sequential(self, business_flow_files: list, rotate_api_key: bool = False) -> None:
        """Fallback method cho xử lý tuần tự (không threading)."""
        logging.info(f"Starting sequential processing of {len(business_flow_files)} business flows")
        
        # Tạo screen graph chung trước khi xử lý các business flows
        logging.info("Generating shared screen graph before sequential processing...")
        if not self.generate_shared_screen_graph(rotate_api_key):
            logging.warning("Failed to generate shared screen graph, but continuing with business flows")
        
        successful_flows = 0
        failed_flows = 0
        
        for flow_info in business_flow_files:
            result = self._process_flow_worker(flow_info, rotate_api_key)
            
            if result['success']:
                successful_flows += 1
            else:
                failed_flows += 1
        
        logging.info(f"Sequential processing completed: {successful_flows} successful, {failed_flows} failed")
        
        # Print token summary if enabled
        if self.count_token:
            self.print_token_summary()

    def execute(self, business_process: str = None, rotate_api_key: bool = False, use_threading: bool = True) -> None:
        """Thực hiện toàn bộ quy trình cho tất cả business flows (backward compatibility)."""
        if business_process is not None:
            logging.warning("Single business_process parameter is deprecated. Processing all flows instead.")
        
        self.execute_all_flows(rotate_api_key=rotate_api_key, use_threading=use_threading)

    def set_max_workers(self, max_workers: int) -> None:
        """Cập nhật số lượng worker threads tối đa."""
        if max_workers < 1:
            raise ValueError("max_workers must be at least 1")
        self.max_workers = max_workers
        logging.info(f"Updated max_workers to {max_workers}")

    def set_caching_enabled(self, enabled: bool) -> None:
        """Bật/tắt caching runtime."""
        self.enable_caching = enabled
        status = "enabled" if enabled else "disabled"
        logging.info(f"Caching {status}")

    def is_caching_enabled(self) -> bool:
        """Kiểm tra trạng thái caching hiện tại."""
        return self.enable_caching

    def _find_cache_by_display_name(self, display_name: str):
        """
        Find cache by display name.

        Args:
            display_name: The display name of the cache to find

        Returns:
            Cache object if found, None otherwise
        """
        if not self.client:
            logging.warning(f"Client not initialized, cannot find cache with display name: {display_name}")
            return None

        try:
            for cache in self.client.caches.list():
                if cache.display_name == display_name:
                    logging.info(f"✅ Found cache with display name: {display_name}")
                    return cache

            logging.warning(f"Cache not found with display name: {display_name}")
            logging.info("Available caches:")
            for cache in self.client.caches.list():
                logging.info(f"  - {cache.display_name}")
            return None

        except Exception as e:
            logging.error(f"Error searching for cache: {e}")
            return None

    def get_processing_info(self) -> dict:
        """Trả về thông tin về cấu hình xử lý hiện tại."""
        return {
            "max_workers": self.max_workers,
            "token_counting_enabled": self.count_token,
            "caching_enabled": self.enable_caching,
            "model_type": self.model_type,
            "business_flows_dir": self.business_flows_dir,
            "base_output_dir": self.base_output_dir,
            "shared_screen_graph_path": self._get_shared_screen_graph_path(),
            "screen_graph_generated": self.screen_graph_generated
        }
