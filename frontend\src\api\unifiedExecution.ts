/**
 * Unified Execution API - Single source of truth for pipeline execution
 */

const API_BASE = 'http://localhost:8000/api/v1'

export interface UploadAndPrepareResponse {
  success: boolean
  fileProcessingId: string
  filename: string
  file_path: string
  message: string
}

export interface ExecuteStepRequest {
  stepId: string
  fileId: string
  runId: string
  parameters: Record<string, any>
}

export interface ExecuteStepResponse {
  success: boolean
  stepId: string
  runId: string
  message: string
  error?: string
  artifacts?: string[]
}

/**
 * Upload file and prepare it for processing
 * Returns a fileProcessingId that serves as backend reference
 */
export const uploadAndPrepareFile = async (file: File): Promise<UploadAndPrepareResponse> => {
  const formData = new FormData()
  formData.append('file', file)

  const response = await fetch(`${API_BASE}/upload-and-prepare`, {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.statusText}`)
  }

  return response.json()
}

/**
 * Execute a pipeline step with unified parameters
 * This is the single execution endpoint that all pipeline nodes use
 */
export const executeStep = async (request: ExecuteStepRequest): Promise<ExecuteStepResponse> => {
  const response = await fetch(`${API_BASE}/execute-step`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  })

  if (!response.ok) {
    throw new Error(`Execution failed: ${response.statusText}`)
  }

  return response.json()
}

/**
 * Initialize a new pipeline run session
 * Creates WebSocket connection for real-time logging
 */
export const initializePipelineRun = async (fileId: string): Promise<{ runId: string }> => {
  const response = await fetch(`${API_BASE}/initialize-run`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ fileId }),
  })

  if (!response.ok) {
    throw new Error(`Failed to initialize run: ${response.statusText}`)
  }

  return response.json()
}

/**
 * Get the status of all steps in a pipeline run
 */
export const getPipelineRunStatus = async (runId: string): Promise<Record<string, string>> => {
  const response = await fetch(`${API_BASE}/run/${runId}/status`)

  if (!response.ok) {
    throw new Error(`Failed to get run status: ${response.statusText}`)
  }

  const data = await response.json()
  return data.step_statuses
}
