import React, { useState } from 'react'
import { ChevronRight, ChevronDown, FileText, Folder, FolderOpen } from 'lucide-react'

interface DocumentOutlineViewerProps {
  data: any
}

interface TreeNodeProps {
  data: any
  level: number
  path: string
}

const TreeNode: React.FC<TreeNodeProps> = ({ data, level, path }) => {
  const [isExpanded, setIsExpanded] = useState(level < 2) // Auto-expand first 2 levels

  const isObject = typeof data === 'object' && data !== null && !Array.isArray(data)
  const isArray = Array.isArray(data)
  const hasChildren = isObject || isArray

  const getIcon = () => {
    if (hasChildren) {
      return isExpanded ? <FolderOpen className="w-4 h-4" /> : <Folder className="w-4 h-4" />
    }
    return <FileText className="w-4 h-4" />
  }

  const getValuePreview = () => {
    if (isObject) {
      const keys = Object.keys(data)
      return `{${keys.length} ${keys.length === 1 ? 'property' : 'properties'}}`
    }
    if (isArray) {
      return `[${data.length} ${data.length === 1 ? 'item' : 'items'}]`
    }
    if (typeof data === 'string') {
      return data.length > 50 ? `"${data.substring(0, 50)}..."` : `"${data}"`
    }
    return String(data)
  }

  const renderChildren = () => {
    if (!isExpanded || !hasChildren) return null

    if (isArray) {
      return data.map((item: any, index: number) => (
        <TreeNode
          key={index}
          data={item}
          level={level + 1}
          path={`${path}[${index}]`}
        />
      ))
    }

    if (isObject) {
      return Object.entries(data).map(([key, value]) => (
        <div key={key} className="ml-4">
          <div className="flex items-center space-x-2 py-1 hover:bg-dark-800 rounded px-2">
            <span className="text-primary-400 text-sm font-mono">{key}:</span>
            <TreeNode
              data={value}
              level={level + 1}
              path={`${path}.${key}`}
            />
          </div>
        </div>
      ))
    }

    return null
  }

  return (
    <div>
      <div 
        className="flex items-center space-x-2 py-1 hover:bg-dark-800 rounded px-2 cursor-pointer"
        onClick={() => hasChildren && setIsExpanded(!isExpanded)}
        style={{ paddingLeft: `${level * 16}px` }}
      >
        {hasChildren && (
          <button className="text-dark-400 hover:text-white">
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </button>
        )}
        
        {!hasChildren && <div className="w-4" />}
        
        <div className="text-dark-400">
          {getIcon()}
        </div>
        
        <span className="text-white text-sm flex-1">
          {getValuePreview()}
        </span>
        
        <span className="text-xs text-dark-500 font-mono">
          {typeof data}
        </span>
      </div>
      
      {renderChildren()}
    </div>
  )
}

export const DocumentOutlineViewer: React.FC<DocumentOutlineViewerProps> = ({ data }) => {
  const [searchTerm, setSearchTerm] = useState('')

  if (!data) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-dark-400">No data to display</p>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Search Header */}
      <div className="p-4 border-b border-dark-700 bg-dark-900">
        <div className="flex items-center space-x-4">
          <h3 className="font-medium text-white">Document Structure</h3>
          <input
            type="text"
            placeholder="Search in document..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 bg-dark-800 border border-dark-600 text-white px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
      </div>

      {/* Tree View */}
      <div className="flex-1 overflow-auto p-4">
        <div className="space-y-1">
          <TreeNode data={data} level={0} path="root" />
        </div>
      </div>

      {/* Stats Footer */}
      <div className="p-4 border-t border-dark-700 bg-dark-900">
        <div className="flex items-center justify-between text-sm text-dark-400">
          <span>
            Type: {Array.isArray(data) ? 'Array' : typeof data}
          </span>
          {Array.isArray(data) && (
            <span>{data.length} items</span>
          )}
          {typeof data === 'object' && !Array.isArray(data) && (
            <span>{Object.keys(data).length} properties</span>
          )}
        </div>
      </div>
    </div>
  )
}
