{"benchmarkTitle": "Test Case Evaluation Benchmark", "benchmarkVersion": "2.2", "purpose": "This benchmark provides a standardized framework for evaluating the quality, correctness, and completeness of generated test cases. It aims to ensure test cases are effective, maintainable, and accurately reflect specified requirements, catering to various test case formats including detailed E2E scenarios.", "instructionsForReviewer": ["Evaluate each individual test case object against Sections I, II, and III.", "Evaluate the entire set of generated test cases for a specific business flow/feature against Section IV.", "For each criterion, set 'compliance' to true (met), false (not met), or null (not applicable/unchecked).", "Provide specific comments in the 'comments' field for any criteria not met or requiring clarification."], "evaluationSections": [{"sectionId": "I", "title": "Structural & Format Compliance", "criteria": [{"id": "SFC-1", "description": "Valid JSON Structure: The test case file contains a single, well-formed JSON object (recommended), or a JSON array containing exactly one test case object. The test case object itself is well-formed.", "compliance": null, "comments": ""}, {"id": "SFC-2", "description": "Field Completeness: All mandatory fields are present in the test case object: TEST CASE ID, TEST SCENARIO, TEST CASE TITLE, PRE-CONDITION, TEST STEPS, TEST DATA, EXPECTED RESULT, POST-CONDITION.", "compliance": null, "comments": ""}, {"id": "SFC-3", "description": "No Extraneous Fields: The test case object contains only the defined mandatory and any explicitly allowed optional fields.", "compliance": null, "comments": ""}, {"id": "SFC-4", "description": "Field Name Convention: All field names within the test case object adhere to the project's defined naming convention (e.g., 'ALL_CAPS_WITH_SPACES', 'camelCase').", "details": ["Example for current format: 'TEST CASE ID', 'TEST STEPS'."], "compliance": null, "comments": ""}]}, {"sectionId": "II", "title": "Field-Specific Quality Criteria", "fieldCriteria": [{"fieldName": "TEST CASE ID", "criteria": [{"id": "FSQC-TCID-1", "description": "Uniqueness: The ID is globally unique across all test cases.", "compliance": null, "comments": ""}, {"id": "FSQC-TCID-2", "description": "Format & Sequence: The ID follows a defined, consistent format (e.g., TC-[Feature/Module]-[ScenarioType]-[SequentialNumber], like TC-Login-Positive-001). Sequential numbering is logical within its primary grouping (e.g., 'TC-Testcase_X').", "compliance": null, "comments": ""}]}, {"fieldName": "TEST SCENARIO", "criteria": [{"id": "FSQC-TSC-1", "description": "Traceability: Description directly maps to and is derived from input scenario parameters or requirements.", "compliance": null, "comments": ""}, {"id": "FSQC-TSC-2", "description": "Clarity & Conciseness: Description is unambiguous, understandable, and clearly states the overall goal/scope of the scenario being tested, without excessive detail better suited for steps or expected results.", "compliance": null, "comments": ""}]}, {"fieldName": "TEST CASE TITLE", "criteria": [{"id": "FSQC-TCT-1", "description": "Precision & Uniqueness: Title accurately, concisely, and uniquely summarizes the test's specific objective.", "compliance": null, "comments": ""}, {"id": "FSQC-TCT-2", "description": "Action-Oriented & Outcome-Focused: Clearly indicates the main action/verification and expected high-level outcome (e.g., \"Verify Successful User Login\", \"E2E - Training Request Full Approval Chain\").", "compliance": null, "comments": ""}, {"id": "FSQC-TCT-3", "description": "Clarity: Title is easily understandable without reading the entire test case.", "compliance": null, "comments": ""}]}, {"fieldName": "PRE-CONDITION", "criteria": [{"id": "FSQC-PRE-1", "description": "Completeness & Accuracy: Lists ALL necessary environmental states, system configurations, user roles/permissions, and pre-existing data required *before the first test step begins*.", "details": ["If login is part of the test steps, pre-condition is 'User is on Login Page' (or similar).", "If a logged-in state is assumed *before* the first step, pre-condition must state: 'User [username] is logged in as [role] and is on [StartingPage].'", "Common suite-wide pre-conditions can be referenced if clearly documented elsewhere, but test-specific data/state must be listed here."], "compliance": null, "comments": ""}, {"id": "FSQC-PRE-2", "description": "Necessity & Minimality: All listed pre-conditions are essential for this test case; no unnecessary setup included.", "compliance": null, "comments": ""}, {"id": "FSQC-PRE-3", "description": "Verifiability: The state defined by pre-conditions should be clear and ideally verifiable.", "compliance": null, "comments": ""}]}, {"fieldName": "TEST STEPS", "criteria": [{"id": "FSQC-TS-1", "description": "Atomicity: Each step describes a SINGLE, distinct, user-perceivable action or system interaction. Avoid combining multiple actions into one step description.", "details": ["Bad example: \"Enter username, password, check 'Remember Me', and click Login.\"", "Good (multiple steps): 1. Enter '[UsernameValue]' into 'Username' field. 2. Enter '[PasswordValue]' into 'Password' field. 3. Check 'Remember Me' checkbox. 4. Click 'Login' button."], "compliance": null, "comments": ""}, {"id": "FSQC-TS-2", "description": "Detail & Precision:", "details": ["Actions are hyper-detailed (e.g., \"User enters '[UsernameValue]' into 'Username' field,\" \"User clicks 'Login' button\").", "Specifies exact screen/page/module where action occurs (using the 'screen' property per step object).", "UI element identifiers (field names, button labels used in step descriptions) are exact, unambiguous, and consistent."], "compliance": null, "comments": ""}, {"id": "FSQC-TS-3", "description": "Data Usage (in step descriptions):", "details": ["Uses only concrete, mapped data values explicitly defined in TEST DATA for this test case (e.g., 'staff.user', 'ValidPassword123!').", "Alternatively, uses clear placeholders (e.g., `[StaffUsername]`, `[StaffPassword]`) that directly and uniquely correspond to keys in TEST DATA.", "Forbids generic terms like \"valid email\" or \"any product\" in step descriptions if specific data is required; the specific instance from TEST DATA must be referenced/used."], "compliance": null, "comments": ""}, {"id": "FSQC-TS-4", "description": "Sequential Logic: Steps follow a logical, end-to-end flow necessary to achieve the test case objective. No redundant or missing steps for the intended path.", "compliance": null, "comments": ""}, {"id": "FSQC-TS-5", "description": "Clarity & User-Centric Language: Steps are described clearly, often from a user's perspective (e.g., \"User navigates to...\") or as clear system actions.", "compliance": null, "comments": ""}]}, {"fieldName": "TEST DATA", "criteria": [{"id": "FSQC-TD-1", "description": "Accuracy & Mapping: All parameters and their concrete values directly and accurately match data used or referenced by placeholders in TEST STEPS. Data is appropriate for the scenario.", "details": ["Ensure distinct users have distinct credentials unless shared ones are a feature."], "compliance": null, "comments": ""}, {"id": "FSQC-TD-2", "description": "Completeness: Lists ALL parameters and their specific values that are *input* into the application via TEST STEPS for this test case.", "compliance": null, "comments": ""}, {"id": "FSQC-TD-3", "description": "Relevance: No unused parameters or data values are listed. Only includes data actively input or used by the test steps.", "compliance": null, "comments": ""}, {"id": "FSQC-TD-4", "description": "Clarity of Values: Data values are clearly presented (e.g., strings in quotes, numbers as numbers).", "compliance": null, "comments": ""}]}, {"fieldName": "EXPECTED RESULT", "criteria": [{"id": "FSQC-ER-1", "description": "Verifiability & Objectivity: Outcome(s) are specific, observable, measurable, and objective.", "compliance": null, "comments": ""}, {"id": "FSQC-ER-2", "description": "Precision (Positive Test - E2E/Multi-stage focus):", "details": ["For E2E tests with multiple stages (e.g., user logins, approvals): Clearly describe the key observable outcome of the *entire test case objective* (e.g., final status, critical message).", "May also briefly describe critical intermediate state changes or success messages *if essential to confirm the flow and lead to the final outcome* (e.g., 'Request status updates to Pending Director A<PERSON><PERSON><PERSON>').", "Describes exact success message(s) and precise location(s) for key verifications.", "Describes new application state (e.g., 'User is redirected to Dashboard page', 'Request XYZ shows status Approved').", "Verifies specific UI elements on resulting screen(s) for key verifications.", "Confirms absence of unexpected errors."], "compliance": null, "comments": ""}, {"id": "FSQC-ER-3", "description": "Precision (Negative Test):", "details": ["Specifies the EXACT error message text.", "Specifies the PRECISE location of the error message or UI failure indication.", "Describes other observable failure indicators (e.g., button disabled, user remains on same page).", "Confirms no unintended positive outcome occurred and application remains in a state consistent with the failure."], "compliance": null, "comments": ""}]}, {"fieldName": "POST-CONDITION", "criteria": [{"id": "FSQC-POST-1", "description": "Verifiability & Accuracy: Describes the precise, verifiable, final stable state of the application and relevant data *after all test steps and verifications are complete*.", "compliance": null, "comments": ""}, {"id": "FSQC-POST-2", "description": "Relevance & Distinctness: The state described is a direct, logical consequence. It's distinct from the primary expected result (e.g., user logged out, specific data persisted, system ready for next isolated test).", "compliance": null, "comments": ""}, {"id": "FSQC-POST-3", "description": "Cleanup (Optional but Recommended): Specifies any necessary cleanup steps if not handled by test isolation (e.g., 'Delete created user [TestUser]').", "compliance": null, "comments": ""}]}]}, {"sectionId": "III", "title": "Overall Quality & Adherence to Rules", "criteria": [{"id": "OQA-1", "description": "Zero Ambiguity: Avoids vague terms. All actions, data, expectations are explicit and precise.", "compliance": null, "comments": ""}, {"id": "OQA-2", "description": "Isolation & Independence: Self-contained; execution/outcome independent of *other test cases*. (E2E tests are internally complex but should not rely on a previous test case's state).", "compliance": null, "comments": ""}, {"id": "OQA-3", "description": "Reproducibility: Consistently yields the same result given same pre-conditions and application version.", "compliance": null, "comments": ""}, {"id": "OQA-4", "description": "Failure Handling (Negative Tests): TEST STEPS proceed only to the point of designed failure. EXPECTED RESULT documents that failure. Subsequent success-path steps are omitted.", "compliance": null, "comments": ""}, {"id": "OQA-5", "description": "Maintainability: Clearly written, uses stable identifiers where possible, structured for easy understanding, update, and debugging.", "compliance": null, "comments": ""}, {"id": "OQA-6", "description": "Conciseness (without sacrificing clarity): Avoids excessive verbosity if conciseness improves readability, provided all necessary detail is present.", "compliance": null, "comments": ""}]}, {"sectionId": "IV", "title": "Business Flow Coverage & Adherence (Suite-Level Evaluation)", "introduction": "This section evaluates the entire set of test cases generated for a specific business flow, feature, or user story.", "subSections": [{"title": "Path & Scenario Coverage", "criteria": [{"id": "BFC-PSC-1", "description": "Completeness: Does the set of test cases collectively cover:", "details": ["Primary success path(s)?", "Key alternative paths?", "Common negative paths (invalid inputs, error conditions, role-based failures)?", "Identified edge cases or boundary conditions?", "All significant decision branches from business flow/requirements?"], "compliance": null, "comments": ""}, {"id": "BFC-PSC-2", "description": "Scenario Mapping: Can each significant step, decision point, user role interaction, or requirement be clearly mapped to one or more test cases?", "compliance": null, "comments": ""}, {"id": "BFC-PSC-3", "description": "Data Variation: Does the set adequately explore meaningful data variations across paths to uncover data-specific issues (e.g., different valid formats, user types, boundary values for inputs)?", "compliance": null, "comments": ""}]}, {"title": "Flow Adherence & Correctness (per relevant test case within the suite)", "criteria": [{"id": "BFC-FAC-1", "description": "Logical Sequence: Do TEST STEPS in each E2E test case accurately reflect the sequence for its intended path from the business flow, including necessary persona switches (logins/logouts)?", "compliance": null, "comments": ""}, {"id": "BFC-FAC-2", "description": "Correctness of Implementation: Does each test case correctly implement conditions, data, and expected outcomes for its specific path and user personas involved?", "compliance": null, "comments": ""}, {"id": "BFC-FAC-3", "description": "No Gaps or Unnecessary Redundancies (Suite Level): Are there obvious gaps in flow coverage, or multiple test cases testing the exact same narrow condition/path segment unnecessarily?", "compliance": null, "comments": ""}]}]}]}