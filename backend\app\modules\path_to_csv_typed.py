"""
Type-hinted Path to CSV Converter Module
Provides strict typing and contracts for path to CSV conversion functionality.
"""
import os
import sys
import logging
import importlib.util
from typing import Dict, Any, Optional
from pathlib import Path

from ..contracts.api_contracts import PathToCsvParameters, PipelineStepResult
from ..core.log_emitter import LogEmitter

# Get the root directory for imports
root_dir = Path(__file__).parent.parent.parent.parent

async def execute(
    params: PathToCsvParameters,
    logger: LogEmitter,
    run_id: str,
    path_processor_path: str,
    output_dir: str,
    screen_variables_path: Optional[str] = None,
    **kwargs
) -> PipelineStepResult:
    """
    Convert screen paths to CSV format using the user's ScreenTestCaseGenerator class.
    
    Args:
        path_processor_path: Path to path processor directory or file
        output_dir: Dynamic output directory for this session
        logger: LogEmitter instance for centralized logging
        run_id: Pipeline run identifier
        screen_variables_path: Optional path to screen variables file
        include_all_combinations: Whether to include all possible combinations
        max_combinations_per_flow: Maximum combinations per business flow (1-200)
        count_token: Whether to count tokens and calculate costs
        **kwargs: Additional parameters
        
    Returns:
        PipelineStepResult: Structured result with status, message, and output files
        
    Raises:
        FileNotFoundError: If required input files are not found
        ImportError: If ScreenTestCaseGenerator class cannot be imported
        ValueError: If parameters are invalid
    """
    step_id = kwargs.get("step_id", "path_to_csv")

    try:
        # Parameters are already validated as Pydantic model
        
        await logger.log(run_id, "INFO", "🚀 Starting Path to CSV Conversion...", step_id)
        
        # Log configuration as a block
        config_info = f"""📋 Path to CSV Configuration:
   • Path processor path: {path_processor_path}
   • Output directory: {output_dir}
   • Include all combinations: {params.include_all_combinations}
   • Max combinations per flow: {params.max_combinations_per_flow}
   • Count tokens: {params.count_token}"""
        await logger.log_block(run_id, "Configuration", config_info, step_id)
        
        # Setup paths using provided output_dir
        csv_output_dir = os.path.join(output_dir, "test_case_csv_generator")
        os.makedirs(csv_output_dir, exist_ok=True)
        
        await logger.log(run_id, "INFO", f"📁 Created CSV output directory: {csv_output_dir}", step_id)
        
        # Find required input files
        # 1. Path processor directory
        path_processor_dir = path_processor_path
        if not os.path.isdir(path_processor_dir):
            path_processor_dir = os.path.join(output_dir, "path_processor")
        
        if not os.path.exists(path_processor_dir):
            await logger.log(run_id, "ERROR", f"❌ Path processor directory not found: {path_processor_dir}", step_id)
            raise FileNotFoundError(f"Path processor directory not found: {path_processor_dir}")
        
        await logger.log(run_id, "INFO", f"📁 Using path processor directory: {path_processor_dir}", step_id)
        
        # 2. Screen variables file (optional)
        screen_vars_file = screen_variables_path
        if not screen_vars_file or not os.path.exists(screen_vars_file):
            screen_vars_file = os.path.join(output_dir, "screen_variables.json")
        
        if os.path.exists(screen_vars_file):
            await logger.log(run_id, "INFO", f"📄 Using screen variables file: {screen_vars_file}", step_id)
        else:
            await logger.log(run_id, "WARNING", f"⚠️ Screen variables file not found: {screen_vars_file}", step_id)
            await logger.log(run_id, "INFO", "   Proceeding without screen variables...", step_id)
        
        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))
        
        # Import and use the user's ScreenTestCaseGenerator class
        await logger.log(run_id, "INFO", "📦 Importing ScreenTestCaseGenerator class...", step_id)
        
        try:
            # Try different import approaches
            try:
                from back_end.path_to_csv import ScreenTestCaseGenerator
                await logger.log(run_id, "SUCCESS", "✅ Successfully imported ScreenTestCaseGenerator (direct import)", step_id)
            except ImportError:
                # Fallback to dynamic import
                await logger.log(run_id, "INFO", "   Trying dynamic import...", step_id)
                spec = importlib.util.spec_from_file_location(
                    "path_to_csv",
                    os.path.join(root_dir, "back_end", "path_to_csv.py")
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                ScreenTestCaseGenerator = module.ScreenTestCaseGenerator
                await logger.log(run_id, "SUCCESS", "✅ Successfully imported ScreenTestCaseGenerator (dynamic import)", step_id)
                
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to import ScreenTestCaseGenerator: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Failed to import ScreenTestCaseGenerator: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Setup configuration file path
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")
        
        # Initialize the user's ScreenTestCaseGenerator
        await logger.log(run_id, "INFO", "🔧 Initializing ScreenTestCaseGenerator...", step_id)
        
        try:
            generator = ScreenTestCaseGenerator(
                path_processor_dir=path_processor_dir,
                screen_variables_path=screen_vars_file if os.path.exists(screen_vars_file) else None,
                output_dir=csv_output_dir,
                config_file=config_file,
                include_all_combinations=params.include_all_combinations,
                max_combinations_per_flow=params.max_combinations_per_flow,
                count_token=params.count_token
            )
            await logger.log(run_id, "SUCCESS", "✅ ScreenTestCaseGenerator initialized successfully", step_id)
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to initialize ScreenTestCaseGenerator: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Failed to initialize ScreenTestCaseGenerator: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Execute path to CSV conversion
        await logger.log(run_id, "INFO", "🚀 Starting path to CSV conversion...", step_id)
        await logger.log(run_id, "INFO", f"   Max combinations per flow: {params.max_combinations_per_flow}", step_id)
        
        try:
            # Call the execute method
            result = generator.execute()
            
            if result and result.get("status") == "success":
                await logger.log(run_id, "SUCCESS", "✅ Path to CSV conversion completed successfully", step_id)
                
                # Log output files
                output_files = []
                if os.path.exists(csv_output_dir):
                    output_files = [os.path.join(csv_output_dir, f) for f in os.listdir(csv_output_dir)]
                    if output_files:
                        files_info = "\n".join([f"   • {os.path.basename(f)}" for f in output_files])
                        await logger.log_block(run_id, "Generated Files", f"📄 Output Files:\n{files_info}", step_id)
                
                # Log conversion statistics if available
                if result.get("csv_files_generated"):
                    stats_info = f"""📊 Conversion Statistics:
   • CSV files generated: {result.get('csv_files_generated', 0)}
   • Total test cases: {result.get('total_test_cases', 0)}
   • Processing time: {result.get('processing_time', 'N/A')}"""
                    await logger.log_block(run_id, "Statistics", stats_info, step_id)
                
                return PipelineStepResult(
                    status="success",
                    message="Path to CSV conversion completed successfully",
                    step_id=step_id,
                    output_files=output_files,
                    metadata={
                        "include_all_combinations": params.include_all_combinations,
                        "max_combinations_per_flow": params.max_combinations_per_flow,
                        "count_token": params.count_token,
                        "csv_files_generated": result.get("csv_files_generated", 0),
                        "total_test_cases": result.get("total_test_cases", 0)
                    }
                )
            else:
                error_msg = result.get("message", "Unknown error") if result else "No result returned"
                await logger.log(run_id, "ERROR", f"❌ Path to CSV conversion failed: {error_msg}", step_id)
                return PipelineStepResult(
                    status="error",
                    message=f"Path to CSV conversion failed: {error_msg}",
                    step_id=step_id,
                    error_details=str(result) if result else "No result returned"
                )
                
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Exception during path to CSV conversion: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Exception during path to CSV conversion: {e}",
                step_id=step_id,
                error_details=str(e)
            )
            
    except Exception as e:
        await logger.log(run_id, "ERROR", f"❌ Unexpected error in path to CSV conversion: {e}", step_id)
        return PipelineStepResult(
            status="error",
            message=f"Unexpected error: {e}",
            step_id=step_id,
            error_details=str(e)
        )
