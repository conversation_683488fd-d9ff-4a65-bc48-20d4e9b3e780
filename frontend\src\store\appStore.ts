/**
 * PHOENIX PROJECT: Global Application State
 * The SINGLE SOURCE OF TRUTH for all application state.
 * Rebuilt from the ground up with absolute precision.
 *
 * MIGRATION: Unified store combining appStore + useStore features
 */
import { create } from "zustand";

// PHOENIX BLUEPRINT: Type Definitions
export interface LogMessage {
    timestamp: string;
    level: string;
    module: string;
    message: string;
    run_id: string;
    step_id?: string;
    metadata?: Record<string, any>;
}

export interface CurrentFile {
    id: string;
    name: string;
}

export type NodeStatus = "idle" | "running" | "success" | "error";

export interface GlobalStats {
    tokens: number;
    cost: number;
}

// MIGRATION: Types from useStore
export interface PipelineNode {
    id: string;
    type: string;
    position: { x: number; y: number };
    data: {
        label: string;
        description: string;
        status: "pending" | "running" | "success" | "failed";
        inputs: string[];
        outputs: string[];
        parameters: Record<string, any>;
        configurable_params: Record<string, any>;
    };
}

export interface PipelineEdge {
    id: string;
    source: string;
    target: string;
    type: string;
    animated: boolean;
    data?: {
        artifact: string;
    };
}

export interface SystemLog {
    timestamp: string;
    message: string;
    level: "info" | "success" | "warning" | "error";
}

export interface WebSocketMessage {
    type:
        | "step_update"
        | "terminal_output"
        | "cost_update"
        | "connection"
        | "pong"
        | "log";
    data?: any;
    step_id?: string;
    status?: string;
    level?: string;
    message?: string;
    module?: string;
    timestamp?: string;
    run_id?: string;
    progress?: number;
}

// PHOENIX BLUEPRINT: Unified Application State Interface
export interface AppState {
    // Core State (Original appStore)
    currentFile: CurrentFile | null;
    pipelineRunId: string | null;
    nodeStatuses: Record<string, NodeStatus>;
    globalStats: GlobalStats;
    logs: LogMessage[];

    // MIGRATION: Pipeline Graph State (from useStore)
    nodes: PipelineNode[];
    edges: PipelineEdge[];

    // MIGRATION: File & Execution State (from useStore)
    uploadedFile: File | null;
    fileProcessingId: string | null;
    stepStatuses: Record<string, "pending" | "running" | "success" | "error">;
    isExecuting: boolean;

    // MIGRATION: Terminal & Logs (from useStore)
    terminalOutput: string[];
    systemLogs: SystemLog[];

    // MIGRATION: Pipeline Execution (from useStore)
    isRunning: boolean;
    currentStep: string | null;

    // MIGRATION: Enhanced Cost Tracking (from useStore)
    currentCost: number;
    totalTokens: number;
    stepCosts: Record<string, { tokens: number; cost: number }>;

    // MIGRATION: Session Management (from useStore)
    sessionId: string;

    // UI State
    selectedNodeId: string | null;
    isUploading: boolean;

    // WebSocket State
    websocket: WebSocket | null;
    isConnected: boolean;

    // Core Actions
    setCurrentFile: (file: CurrentFile | null) => void;
    setPipelineRunId: (runId: string | null) => void;
    setNodeStatus: (nodeId: string, status: NodeStatus) => void;
    updateGlobalStats: (stats: Partial<GlobalStats>) => void;
    addLog: (log: LogMessage) => void;
    clearLogs: () => void;
    setSelectedNodeId: (nodeId: string | null) => void;
    setIsUploading: (uploading: boolean) => void;

    // MIGRATION: Pipeline Graph Actions (from useStore)
    setNodes: (nodes: PipelineNode[]) => void;
    setEdges: (edges: PipelineEdge[]) => void;
    updateNodeStatus: (
        nodeId: string,
        status: "pending" | "running" | "success" | "error"
    ) => void;
    setStepStatuses: (
        statuses: Record<string, "pending" | "running" | "success" | "error">
    ) => void;

    // MIGRATION: File & Execution Actions (from useStore)
    setUploadedFile: (file: File | null) => void;
    setFileProcessingId: (id: string | null) => void;
    initializeNewPipelineRun: () => string;

    // MIGRATION: Terminal Actions (from useStore)
    addTerminalOutput: (output: string) => void;
    clearTerminalOutput: () => void;
    addSystemLog: (
        message: string,
        level?: "info" | "success" | "warning" | "error"
    ) => void;

    // MIGRATION: Pipeline Execution Actions (from useStore)
    runPipeline: () => void;
    stopPipeline: () => void;
    setCurrentStep: (step: string | null) => void;

    // MIGRATION: Cost Tracking Actions (from useStore)
    updateCost: (cost: number) => void;

    // MIGRATION: Session Actions (from useStore)
    setSessionId: (sessionId: string) => void;

    // WebSocket Actions
    connectWebSocket: () => void;
    disconnectWebSocket: () => void;
    handleWebSocketMessage: (message: WebSocketMessage) => void;

    // Computed Actions
    resetPipeline: () => void;
    startExecution: (nodeId: string) => void;
}

// PHOENIX BLUEPRINT: Unified Initial State
const initialState = {
    // Core State (Original appStore)
    currentFile: null,
    pipelineRunId: null,
    nodeStatuses: {
        document_processor: "idle" as NodeStatus,
        business_flow_detector: "idle" as NodeStatus,
        relevant_content_processor: "idle" as NodeStatus,
        path_finder: "idle" as NodeStatus,
        path_to_csv: "idle" as NodeStatus,
        test_case_generator: "idle" as NodeStatus,
        test_case_evaluator: "idle" as NodeStatus,
    },
    globalStats: {
        tokens: 0,
        cost: 0,
    },
    logs: [],

    // MIGRATION: Pipeline Graph State (from useStore)
    nodes: [],
    edges: [],

    // MIGRATION: File & Execution State (from useStore)
    uploadedFile: null,
    fileProcessingId: null,
    stepStatuses: {},
    isExecuting: false,

    // MIGRATION: Terminal & Logs (from useStore)
    terminalOutput: [],
    systemLogs: [],

    // MIGRATION: Pipeline Execution (from useStore)
    isRunning: false,
    currentStep: null,

    // MIGRATION: Enhanced Cost Tracking (from useStore)
    currentCost: 0,
    totalTokens: 0,
    stepCosts: {},

    // MIGRATION: Session Management (from useStore)
    sessionId: "default",

    // UI State
    selectedNodeId: null,
    isUploading: false,

    // WebSocket State
    websocket: null,
    isConnected: false,
};

// PHOENIX BLUEPRINT: Zustand Store
export const useAppStore = create<AppState>((set, get) => ({
    ...initialState,

    // Core Actions
    setCurrentFile: (file) => set({ currentFile: file }),

    setPipelineRunId: (runId) => set({ pipelineRunId: runId }),

    setNodeStatus: (nodeId, status) =>
        set((state) => ({
            nodeStatuses: {
                ...state.nodeStatuses,
                [nodeId]: status,
            },
        })),

    updateGlobalStats: (stats) =>
        set((state) => ({
            globalStats: {
                ...state.globalStats,
                ...stats,
            },
        })),

    addLog: (log) =>
        set((state) => ({
            logs: [...state.logs, log],
        })),

    clearLogs: () => set({ logs: [] }),

    setSelectedNodeId: (nodeId) => set({ selectedNodeId: nodeId }),

    setIsUploading: (uploading) => set({ isUploading: uploading }),

    // MIGRATION: Pipeline Graph Actions (from useStore)
    setNodes: (nodes) => set({ nodes }),
    setEdges: (edges) => set({ edges }),

    updateNodeStatus: (nodeId, status) => {
        const { nodes } = get();
        const updatedNodes = nodes.map((node) =>
            node.id === nodeId
                ? { ...node, data: { ...node.data, status: status as any } }
                : node
        );
        set({ nodes: updatedNodes });

        // Update step statuses
        const { stepStatuses } = get();
        set({ stepStatuses: { ...stepStatuses, [nodeId]: status } });
    },

    setStepStatuses: (statuses) => {
        set({ stepStatuses: statuses });

        // Update node statuses
        const { nodes } = get();
        const updatedNodes = nodes.map((node) => ({
            ...node,
            data: {
                ...node.data,
                status: (statuses[node.id] || "pending") as any,
            },
        }));
        set({ nodes: updatedNodes });
    },

    // MIGRATION: File & Execution Actions (from useStore)
    setUploadedFile: (file) => set({ uploadedFile: file }),
    setFileProcessingId: (id) => set({ fileProcessingId: id }),

    initializeNewPipelineRun: () => {
        const runId = `run_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 9)}`;
        set({ pipelineRunId: runId });
        return runId;
    },

    // MIGRATION: Terminal Actions (from useStore)
    addTerminalOutput: (output) =>
        set((state) => ({
            terminalOutput: [...state.terminalOutput, output],
        })),

    clearTerminalOutput: () => set({ terminalOutput: [] }),

    addSystemLog: (message, level = "info") =>
        set((state) => ({
            systemLogs: [
                ...state.systemLogs,
                {
                    timestamp: new Date().toISOString(),
                    message,
                    level,
                },
            ],
        })),

    // MIGRATION: Pipeline Execution Actions (from useStore)
    runPipeline: () => {
        set({ isRunning: true });
        get().addSystemLog("Pipeline execution started", "info");
        get().addTerminalOutput("🚀 Starting pipeline execution...");

        // TODO: Make API call to start pipeline
        fetch("/api/v1/pipeline/run", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ session_id: get().sessionId }),
        }).catch((error) => {
            get().addSystemLog(
                `Failed to start pipeline: ${error.message}`,
                "error"
            );
            set({ isRunning: false });
        });
    },

    stopPipeline: () => {
        set({ isRunning: false, currentStep: null });
        get().addSystemLog("Pipeline execution stopped", "warning");
        get().addTerminalOutput("⏹️ Pipeline execution stopped");

        // TODO: Make API call to stop pipeline
        fetch("/api/v1/pipeline/stop", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ session_id: get().sessionId }),
        }).catch((error) => {
            get().addSystemLog(
                `Failed to stop pipeline: ${error.message}`,
                "error"
            );
        });
    },

    setCurrentStep: (step) => set({ currentStep: step }),

    // MIGRATION: Cost Tracking Actions (from useStore)
    updateCost: (cost) => set({ currentCost: cost }),

    // MIGRATION: Session Actions (from useStore)
    setSessionId: (sessionId) => {
        set({ sessionId });
        // Reconnect WebSocket with new session ID
        get().connectWebSocket();
    },

    // WebSocket Actions
    connectWebSocket: () => {
        const state = get();
        const runId = state.pipelineRunId;

        console.log("🔌 Attempting WebSocket connection for runId:", runId);

        if (!runId) {
            console.warn("⚠️ No runId available for WebSocket connection");
            return;
        }

        if (state.websocket?.readyState === WebSocket.CONNECTING) {
            console.log("🔄 WebSocket already connecting");
            return;
        }

        // Close existing connection
        if (state.websocket) {
            console.log("🔌 Closing existing WebSocket connection");
            state.websocket.close();
        }

        try {
            const wsUrl = `ws://127.0.0.1:8000/api/v1/ws/logs/${runId}`;
            console.log("🔗 Connecting to WebSocket:", wsUrl);
            const ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                set({ isConnected: true });
                console.log("✅ WebSocket connected for run:", runId);
                console.log("🔍 WebSocket ready state:", ws.readyState);
            };

            ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    // console.log("🔔 WebSocket message received:", message);
                    // console.log("📋 Message type:", message.type);
                    // console.log(
                    //     "📋 Full message data:",
                    //     JSON.stringify(message, null, 2)
                    // );
                    get().handleWebSocketMessage(message);
                } catch (error) {
                    // console.error(
                    //     "❌ Failed to parse WebSocket message:",
                    //     error
                    // );
                    // console.log("📄 Raw message data:", event.data);
                }
            };

            ws.onclose = (event) => {
                set({ isConnected: false, websocket: null });
                console.log(
                    "🔌 WebSocket disconnected. Code:",
                    event.code,
                    "Reason:",
                    event.reason
                );
            };

            ws.onerror = (error) => {
                set({ isConnected: false });
                console.error("❌ WebSocket error:", error);
            };

            set({ websocket: ws });
        } catch (error) {
            // console.error("❌ Failed to connect WebSocket:", error);
        }
    },

    disconnectWebSocket: () => {
        const state = get();
        if (state.websocket) {
            state.websocket.close();
        }
        set({ websocket: null, isConnected: false });
    },

    handleWebSocketMessage: (message) => {
        // console.log("🔄 Processing WebSocket message:", message.type, message);

        switch (message.type) {
            case "step_update": {
                // Backend sends step_id and status at root level, not in data
                const { step_id, status } = message;
                console.log("📊 Step update received:", {
                    step_id,
                    status,
                    fullMessage: message,
                });

                if (step_id && status) {
                    // Map backend status to frontend status
                    let frontendStatus: NodeStatus = "idle";
                    switch (status) {
                        case "running":
                        case "in_progress":
                            frontendStatus = "running";
                            break;
                        case "success":
                        case "completed":
                            frontendStatus = "success";
                            break;
                        case "error":
                        case "failed":
                            frontendStatus = "error";
                            break;
                        default:
                            // console.warn("⚠️ Unknown status received:", status);
                            frontendStatus = "idle";
                    }
                    console.log(
                        "🎯 Setting node status:",
                        step_id,
                        "->",
                        frontendStatus,
                        "(from backend status:",
                        status + ")"
                    );
                    get().setNodeStatus(step_id, frontendStatus);

                    // Add success notification
                    if (frontendStatus === "success") {
                        console.log("✅ Step completed successfully:", step_id);
                    } else if (frontendStatus === "error") {
                        console.log("❌ Step failed:", step_id);
                    }
                } else {
                    console.warn("⚠️ Invalid step_update message:", message);
                }
                break;
            }
            case "log": {
                // Handle log messages and detect step completion
                const { step_id, message: logMessage, level } = message;
                // console.log("📝 Log message received:", {
                //     step_id,
                //     logMessage,
                //     level,
                //     module,
                // });

                // Add log to store
                get().addLog({
                    timestamp: message.timestamp || new Date().toISOString(),
                    level: level?.toUpperCase() || "INFO",
                    message: logMessage || "",
                    module: message.module || "unknown",
                    step_id: step_id,
                    run_id: message.run_id || get().pipelineRunId || "unknown",
                });

                // Detect step completion from log messages
                if (step_id && logMessage) {
                    let detectedStatus: NodeStatus | null = null;

                    // Success patterns
                    if (
                        logMessage.includes("✅ Step completed:") ||
                        logMessage.includes("completed successfully") ||
                        logMessage.includes("🎉") ||
                        level === "SUCCESS" ||
                        level === "success"
                    ) {
                        detectedStatus = "success";
                    }
                    // Error patterns
                    else if (
                        logMessage.includes("❌") ||
                        logMessage.includes("failed:") ||
                        logMessage.includes("Error:") ||
                        level === "ERROR" ||
                        level === "error"
                    ) {
                        detectedStatus = "error";
                    }
                    // Running patterns
                    else if (
                        logMessage.includes("🚀 Starting") ||
                        logMessage.includes("🔧 Executing") ||
                        logMessage.includes("Processing")
                    ) {
                        detectedStatus = "running";
                    }

                    if (detectedStatus) {
                        // console.log(
                        //     "🔍 Detected status from log:",
                        //     step_id,
                        //     "->",
                        //     detectedStatus,
                        //     "(from message:",
                        //     logMessage + ")"
                        // );
                        get().setNodeStatus(step_id, detectedStatus);

                        if (detectedStatus === "success") {
                            // console.log(
                            //     "✅ Step completed successfully (detected from log):",
                            //     step_id
                            // );
                        } else if (detectedStatus === "error") {
                            // console.log(
                            //     "❌ Step failed (detected from log):",
                            //     step_id
                            // );
                        }
                    }
                }
                break;
            }
            default:
            // console.log("❓ Unhandled WebSocket message:", message);
        }
    },

    // Computed Actions
    resetPipeline: () =>
        set({
            ...initialState,
            currentFile: get().currentFile, // Keep the current file
        }),

    startExecution: (nodeId) => {
        const state = get();

        // Generate new run ID if none exists
        if (!state.pipelineRunId) {
            const runId = `run_${Date.now()}_${Math.random()
                .toString(36)
                .substr(2, 9)}`;
            set({ pipelineRunId: runId });
        }

        // Set node to running
        set((state) => ({
            nodeStatuses: {
                ...state.nodeStatuses,
                [nodeId]: "running",
            },
        }));

        // Connect WebSocket to receive status updates
        setTimeout(() => {
            get().connectWebSocket();
        }, 100); // Small delay to ensure runId is set
    },
}));

// PHOENIX BLUEPRINT: Utility Hooks
export const useCurrentFile = () => useAppStore((state) => state.currentFile);
export const usePipelineRunId = () =>
    useAppStore((state) => state.pipelineRunId);
export const useNodeStatuses = () => useAppStore((state) => state.nodeStatuses);
export const useGlobalStats = () => useAppStore((state) => state.globalStats);
export const useLogs = () => useAppStore((state) => state.logs);
export const useSelectedNodeId = () =>
    useAppStore((state) => state.selectedNodeId);
export const useIsUploading = () => useAppStore((state) => state.isUploading);
export const useIsConnected = () => useAppStore((state) => state.isConnected);

// MIGRATION: Additional Utility Hooks (from useStore)
export const useNodes = () => useAppStore((state) => state.nodes);
export const useEdges = () => useAppStore((state) => state.edges);
export const useUploadedFile = () => useAppStore((state) => state.uploadedFile);
export const useFileProcessingId = () =>
    useAppStore((state) => state.fileProcessingId);
export const useStepStatuses = () => useAppStore((state) => state.stepStatuses);
export const useIsExecuting = () => useAppStore((state) => state.isExecuting);
export const useTerminalOutput = () =>
    useAppStore((state) => state.terminalOutput);
export const useSystemLogs = () => useAppStore((state) => state.systemLogs);
export const useIsRunning = () => useAppStore((state) => state.isRunning);
export const useCurrentStep = () => useAppStore((state) => state.currentStep);
export const useCurrentCost = () => useAppStore((state) => state.currentCost);
export const useTotalTokens = () => useAppStore((state) => state.totalTokens);
export const useStepCosts = () => useAppStore((state) => state.stepCosts);
export const useSessionId = () => useAppStore((state) => state.sessionId);

// PHOENIX BLUEPRINT: Unified Action Hooks
export const useAppActions = () =>
    useAppStore((state) => ({
        // Core Actions
        setCurrentFile: state.setCurrentFile,
        setPipelineRunId: state.setPipelineRunId,
        setNodeStatus: state.setNodeStatus,
        updateGlobalStats: state.updateGlobalStats,
        addLog: state.addLog,
        clearLogs: state.clearLogs,
        setSelectedNodeId: state.setSelectedNodeId,
        setIsUploading: state.setIsUploading,

        // MIGRATION: Pipeline Graph Actions
        setNodes: state.setNodes,
        setEdges: state.setEdges,
        updateNodeStatus: state.updateNodeStatus,
        setStepStatuses: state.setStepStatuses,

        // MIGRATION: File & Execution Actions
        setUploadedFile: state.setUploadedFile,
        setFileProcessingId: state.setFileProcessingId,
        initializeNewPipelineRun: state.initializeNewPipelineRun,

        // MIGRATION: Terminal Actions
        addTerminalOutput: state.addTerminalOutput,
        clearTerminalOutput: state.clearTerminalOutput,
        addSystemLog: state.addSystemLog,

        // MIGRATION: Pipeline Execution Actions
        runPipeline: state.runPipeline,
        stopPipeline: state.stopPipeline,
        setCurrentStep: state.setCurrentStep,

        // MIGRATION: Cost Tracking Actions
        updateCost: state.updateCost,

        // MIGRATION: Session Actions
        setSessionId: state.setSessionId,

        // WebSocket Actions
        connectWebSocket: state.connectWebSocket,
        disconnectWebSocket: state.disconnectWebSocket,

        // Computed Actions
        resetPipeline: state.resetPipeline,
        startExecution: state.startExecution,
    }));

// MIGRATION: Compatibility exports for useStore components
export const useStore = useAppStore;
