/**
 * Production-ready WebSocket hook for real-time logging
 */
import { useState, useEffect, useRef, useCallback } from "react";
import {
    WebSocketMessage,
    LogEntry,
    WebSocketState,
    LogMessage,
} from "../types/logging";

interface UseWebSocketLogsOptions {
    maxRetries?: number;
    retryDelay?: number;
    maxRetryDelay?: number;
    maxLogEntries?: number;
    pingInterval?: number;
}

interface UseWebSocketLogsReturn {
    logs: LogEntry[];
    state: WebSocketState;
    sendMessage: (message: any) => void;
    clearLogs: () => void;
    reconnect: () => void;
}

export const useWebSocketLogs = (
    runId: string | null,
    options: UseWebSocketLogsOptions = {}
): UseWebSocketLogsReturn => {
    const {
        maxRetries = 5,
        retryDelay = 1000,
        maxRetryDelay = 30000,
        maxLogEntries = 1000,
        pingInterval = 30000,
    } = options;

    const [logs, setLogs] = useState<LogEntry[]>([]);
    const [state, setState] = useState<WebSocketState>({
        isConnected: false,
        isConnecting: false,
        error: null,
        clientId: null,
        runId: null,
    });

    const wsRef = useRef<WebSocket | null>(null);
    const retryCountRef = useRef(0);
    const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);
    const reconnectAttemptsRef = useRef(0);

    // Generate unique ID for log entries
    const generateLogId = useCallback(() => {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }, []);

    // Format timestamp for display
    const formatTimestamp = useCallback((timestamp: string) => {
        try {
            const date = new Date(timestamp);
            return date.toLocaleTimeString("en-US", {
                hour12: false,
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
                fractionalSecondDigits: 3,
            });
        } catch {
            return timestamp;
        }
    }, []);

    // Add log entry with deduplication and size management
    const addLogEntry = useCallback(
        (logMessage: LogMessage) => {
            const logEntry: LogEntry = {
                ...logMessage,
                id: generateLogId(),
                formattedTime: formatTimestamp(logMessage.timestamp),
            };

            setLogs((prevLogs) => {
                const newLogs = [...prevLogs, logEntry];

                // Limit log entries to prevent memory issues
                if (newLogs.length > maxLogEntries) {
                    return newLogs.slice(-maxLogEntries);
                }

                return newLogs;
            });
        },
        [generateLogId, formatTimestamp, maxLogEntries]
    );

    // Handle WebSocket messages
    const handleMessage = useCallback(
        (event: MessageEvent) => {
            try {
                const message: WebSocketMessage = JSON.parse(event.data);

                switch (message.type) {
                    case "log":
                        addLogEntry(message);
                        break;

                    case "connection_established":
                        setState((prev) => ({
                            ...prev,
                            clientId: message.client_id,
                            runId: message.run_id,
                            error: null,
                        }));
                        break;

                    case "step_update":
                        // Handle step updates (could be used to update UI state)
                        console.log("Step update:", message);
                        break;

                    case "error":
                        setState((prev) => ({
                            ...prev,
                            error: message.message,
                        }));
                        break;

                    case "pong":
                        // Handle pong response
                        break;

                    default:
                        console.warn("Unknown message type:", message);
                }
            } catch (error) {
                console.error("Failed to parse WebSocket message:", error);
            }
        },
        [addLogEntry]
    );

    // Send ping to keep connection alive
    const sendPing = useCallback(() => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(
                JSON.stringify({
                    type: "ping",
                    timestamp: new Date().toISOString(),
                })
            );
        }
    }, []);

    // Send message to WebSocket
    const sendMessage = useCallback((message: any) => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify(message));
        } else {
            console.warn("WebSocket is not connected");
        }
    }, []);

    // Calculate retry delay with exponential backoff
    const getRetryDelay = useCallback(
        (attempt: number) => {
            const delay = Math.min(
                retryDelay * Math.pow(2, attempt),
                maxRetryDelay
            );
            return delay + Math.random() * 1000; // Add jitter
        },
        [retryDelay, maxRetryDelay]
    );

    // Connect to WebSocket
    const connect = useCallback(() => {
        if (!runId || wsRef.current?.readyState === WebSocket.CONNECTING) {
            return;
        }

        // Prevent multiple connection attempts
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            return;
        }

        setState((prev) => ({ ...prev, isConnecting: true, error: null }));

        try {
            const wsUrl = `ws://localhost:8000/api/v1/ws/logs/${runId}`;
            const ws = new WebSocket(wsUrl);
            wsRef.current = ws;

            ws.onopen = () => {
                setState((prev) => ({
                    ...prev,
                    isConnected: true,
                    isConnecting: false,
                    error: null,
                }));

                retryCountRef.current = 0;
                reconnectAttemptsRef.current = 0;

                // Start ping interval
                if (pingIntervalRef.current) {
                    clearInterval(pingIntervalRef.current);
                }
                pingIntervalRef.current = setInterval(sendPing, pingInterval);
            };

            ws.onmessage = handleMessage;

            ws.onclose = (event) => {
                setState((prev) => ({
                    ...prev,
                    isConnected: false,
                    isConnecting: false,
                }));

                // Clear ping interval
                if (pingIntervalRef.current) {
                    clearInterval(pingIntervalRef.current);
                    pingIntervalRef.current = null;
                }

                // Attempt reconnection if not a clean close and not already trying to reconnect
                if (
                    event.code !== 1000 &&
                    retryCountRef.current < maxRetries &&
                    !retryTimeoutRef.current
                ) {
                    const delay = getRetryDelay(retryCountRef.current);
                    retryCountRef.current++;
                    reconnectAttemptsRef.current++;

                    setState((prev) => ({
                        ...prev,
                        error: `Connection lost. Reconnecting in ${Math.round(
                            delay / 1000
                        )}s... (${reconnectAttemptsRef.current}/${maxRetries})`,
                    }));

                    retryTimeoutRef.current = setTimeout(() => {
                        retryTimeoutRef.current = null; // Clear the timeout reference
                        connect();
                    }, delay);
                } else if (retryCountRef.current >= maxRetries) {
                    setState((prev) => ({
                        ...prev,
                        error: "Failed to reconnect after maximum attempts",
                    }));
                }
            };

            ws.onerror = (error) => {
                console.error("WebSocket error:", error);
                setState((prev) => ({
                    ...prev,
                    error: "WebSocket connection error",
                }));
            };
        } catch (error) {
            setState((prev) => ({
                ...prev,
                isConnecting: false,
                error: `Failed to create WebSocket connection: ${error}`,
            }));
        }
    }, [
        runId,
        handleMessage,
        sendPing,
        pingInterval,
        maxRetries,
        getRetryDelay,
    ]);

    // Disconnect WebSocket
    const disconnect = useCallback(() => {
        if (retryTimeoutRef.current) {
            clearTimeout(retryTimeoutRef.current);
            retryTimeoutRef.current = null;
        }

        if (pingIntervalRef.current) {
            clearInterval(pingIntervalRef.current);
            pingIntervalRef.current = null;
        }

        if (wsRef.current) {
            wsRef.current.close(1000, "Component unmounting");
            wsRef.current = null;
        }

        setState({
            isConnected: false,
            isConnecting: false,
            error: null,
            clientId: null,
            runId: null,
        });
    }, []);

    // Manual reconnect
    const reconnect = useCallback(() => {
        disconnect();
        retryCountRef.current = 0;
        reconnectAttemptsRef.current = 0;
        setTimeout(connect, 100);
    }, [disconnect, connect]);

    // Clear logs
    const clearLogs = useCallback(() => {
        setLogs([]);
    }, []);

    // Effect to handle connection lifecycle
    useEffect(() => {
        if (runId) {
            connect();
        } else {
            disconnect();
        }

        return disconnect;
    }, [runId, connect, disconnect]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            disconnect();
        };
    }, [disconnect]);

    return {
        logs,
        state,
        sendMessage,
        clearLogs,
        reconnect,
    };
};
