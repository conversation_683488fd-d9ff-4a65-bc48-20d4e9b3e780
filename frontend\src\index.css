@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Pipeline Node Animations */
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 3s ease infinite;
}

@layer base {
  * {
    @apply border-dark-700;
  }
  
  body {
    @apply bg-dark-950 text-white font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Custom scrollbar for dark theme */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-dark-900;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-dark-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-dark-500;
  }
}

@layer components {
  .glass-panel {
    @apply bg-dark-900/80 backdrop-blur-sm border border-dark-700/50;
  }
  
  .card {
    @apply bg-dark-900 border border-dark-700 rounded-lg;
  }
  
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md transition-colors;
  }
  
  .btn-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-white px-4 py-2 rounded-md transition-colors;
  }
  
  .input {
    @apply bg-dark-800 border border-dark-600 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
  }
  
  .status-pending {
    @apply bg-dark-600 text-dark-200;
  }
  
  .status-running {
    @apply bg-primary-600 text-white animate-pulse-glow;
  }
  
  .status-success {
    @apply bg-success-600 text-white;
  }
  
  .status-failed {
    @apply bg-error-600 text-white;
  }
}

/* React Flow dark theme overrides */
.react-flow__node {
  @apply bg-dark-800 border-dark-600 text-white;
}

.react-flow__edge-path {
  @apply stroke-dark-400;
}

.react-flow__edge.animated .react-flow__edge-path {
  @apply stroke-primary-500;
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

.react-flow__controls {
  @apply bg-dark-800 border-dark-600;
}

.react-flow__controls button {
  @apply bg-dark-700 border-dark-600 text-white hover:bg-dark-600;
}

.react-flow__minimap {
  @apply bg-dark-800 border-dark-600;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* AG Grid dark theme - applied inline */
