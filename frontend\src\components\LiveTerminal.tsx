import React, { useEffect, useRef, useState, useCallback } from "react";
import { Terminal as XTerm } from "xterm";
import { FitAddon } from "xterm-addon-fit";
import { WebLinksAddon } from "xterm-addon-web-links";
import { SearchAddon } from "xterm-addon-search";
import {
    Search,
    Copy,
    Download,
    Maximize2,
    Minimize2,
    Wifi,
    WifiOff,
    Move,
} from "lucide-react";
import "xterm/css/xterm.css";

interface LiveTerminalProps {
    height?: number;
    onResize?: (height: number) => void;
    className?: string;
}

export const LiveTerminal: React.FC<LiveTerminalProps> = ({
    height,
    onResize,
    className = "",
}) => {
    const terminalRef = useRef<HTMLDivElement>(null);
    const xtermRef = useRef<XTerm | null>(null);
    const fitAddonRef = useRef<FitAddon | null>(null);
    const searchAddonRef = useRef<SearchAddon | null>(null);
    const wsRef = useRef<WebSocket | null>(null);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [searchVisible, setSearchVisible] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [connectionStatus, setConnectionStatus] = useState<
        "connecting" | "connected" | "disconnected"
    >("connecting");
    const [logCount, setLogCount] = useState(0);
    const logBuffer = useRef<string[]>([]);
    const [isResizing, setIsResizing] = useState(false);
    const [currentHeight, setCurrentHeight] = useState(height || 400);
    const resizeStartY = useRef<number>(0);
    const resizeStartHeight = useRef<number>(height || 400);

    useEffect(() => {
        if (!terminalRef.current) return;

        // Create terminal instance with VS Code-like theme
        const terminal = new XTerm({
            theme: {
                background: "#1e1e1e",
                foreground: "#d4d4d4",
                cursor: "#ffffff",
                cursorAccent: "#000000",
                selection: "rgba(255, 255, 255, 0.3)",
                black: "#000000",
                red: "#f14c4c",
                green: "#23d18b",
                yellow: "#f5f543",
                blue: "#3b8eea",
                magenta: "#d670d6",
                cyan: "#29b8db",
                white: "#e5e5e5",
                brightBlack: "#666666",
                brightRed: "#f14c4c",
                brightGreen: "#23d18b",
                brightYellow: "#f5f543",
                brightBlue: "#3b8eea",
                brightMagenta: "#d670d6",
                brightCyan: "#29b8db",
                brightWhite: "#e5e5e5",
            },
            fontFamily:
                '"Cascadia Code", "Fira Code", "JetBrains Mono", Consolas, monospace',
            fontSize: 14,
            lineHeight: 1.2,
            cursorBlink: true,
            cursorStyle: "block",
            scrollback: 10000,
            convertEol: true,
            allowTransparency: false,
            minimumContrastRatio: 4.5,
        });

        // Load addons
        const fitAddon = new FitAddon();
        const webLinksAddon = new WebLinksAddon();
        const searchAddon = new SearchAddon();

        terminal.loadAddon(fitAddon);
        terminal.loadAddon(webLinksAddon);
        terminal.loadAddon(searchAddon);

        // Open terminal
        terminal.open(terminalRef.current);
        fitAddon.fit();

        // Store references
        xtermRef.current = terminal;
        fitAddonRef.current = fitAddon;
        searchAddonRef.current = searchAddon;

        // Show initial welcome message
        terminal.writeln(
            "\x1b[1;36m╭─────────────────────────────────────────────────────────────────╮\x1b[0m"
        );
        terminal.writeln(
            "\x1b[1;36m│  🔴 LIVE TERMINAL - Real-time Backend Logs                     │\x1b[0m"
        );
        terminal.writeln(
            "\x1b[1;36m│  Connected to VS Code Terminal Output                          │\x1b[0m"
        );
        terminal.writeln(
            "\x1b[1;36m╰─────────────────────────────────────────────────────────────────╯\x1b[0m"
        );
        terminal.writeln("");
        terminal.writeln("\x1b[33m🔌 Connecting to live log stream...\x1b[0m");
        terminal.writeln("");

        // Connect to WebSocket for live logs
        connectWebSocket();

        // Handle window resize
        const handleWindowResize = () => {
            if (fitAddonRef.current) {
                setTimeout(() => fitAddonRef.current?.fit(), 100);
            }
        };

        window.addEventListener("resize", handleWindowResize);

        // Cleanup
        return () => {
            window.removeEventListener("resize", handleWindowResize);
            if (wsRef.current) {
                wsRef.current.close();
            }
            terminal.dispose();
        };
    }, []);

    // Handle manual resize
    const handleResizeStart = useCallback(
        (e: React.MouseEvent) => {
            e.preventDefault();
            setIsResizing(true);
            resizeStartY.current = e.clientY;
            resizeStartHeight.current = currentHeight;
        },
        [currentHeight]
    );

    const handleResizeMove = useCallback(
        (e: MouseEvent) => {
            if (!isResizing) return;

            const deltaY = e.clientY - resizeStartY.current;
            const newHeight = Math.max(
                200,
                Math.min(800, resizeStartHeight.current - deltaY)
            );

            setCurrentHeight(newHeight);
            onResize?.(newHeight);

            // Fit terminal to new size
            if (fitAddonRef.current) {
                setTimeout(() => fitAddonRef.current?.fit(), 10);
            }
        },
        [isResizing, onResize]
    );

    const handleResizeEnd = useCallback(() => {
        setIsResizing(false);
    }, []);

    // Add mouse event listeners for resizing
    useEffect(() => {
        if (isResizing) {
            document.addEventListener("mousemove", handleResizeMove);
            document.addEventListener("mouseup", handleResizeEnd);
            document.body.style.cursor = "ns-resize";
            document.body.style.userSelect = "none";
        } else {
            document.removeEventListener("mousemove", handleResizeMove);
            document.removeEventListener("mouseup", handleResizeEnd);
            document.body.style.cursor = "";
            document.body.style.userSelect = "";
        }

        return () => {
            document.removeEventListener("mousemove", handleResizeMove);
            document.removeEventListener("mouseup", handleResizeEnd);
            document.body.style.cursor = "";
            document.body.style.userSelect = "";
        };
    }, [isResizing, handleResizeMove, handleResizeEnd]);

    const connectWebSocket = () => {
        try {
            setConnectionStatus("connecting");

            // Connect to the live terminal WebSocket endpoint
            const ws = new WebSocket(
                "ws://localhost:8000/api/v1/ws/terminal/live"
            );
            wsRef.current = ws;

            ws.onopen = () => {
                setConnectionStatus("connected");
                if (xtermRef.current) {
                    xtermRef.current.writeln(
                        "\x1b[32m✅ Connected to live backend terminal\x1b[0m"
                    );
                    xtermRef.current.writeln(
                        "\x1b[90m─────────────────────────────────────────────────────────────────\x1b[0m"
                    );
                    xtermRef.current.writeln("");
                }
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    handleLogMessage(data);
                } catch (error) {
                    // Handle plain text messages
                    handlePlainTextMessage(event.data);
                }
            };

            ws.onclose = () => {
                setConnectionStatus("disconnected");
                if (xtermRef.current) {
                    xtermRef.current.writeln("");
                    xtermRef.current.writeln(
                        "\x1b[31m❌ Connection lost. Attempting to reconnect...\x1b[0m"
                    );
                }

                // Auto-reconnect after 3 seconds
                setTimeout(() => {
                    if (xtermRef.current) {
                        connectWebSocket();
                    }
                }, 3000);
            };

            ws.onerror = (error) => {
                setConnectionStatus("disconnected");
                if (xtermRef.current) {
                    xtermRef.current.writeln(
                        "\x1b[31m❌ WebSocket connection error\x1b[0m"
                    );
                }
            };
        } catch (error) {
            setConnectionStatus("disconnected");
            if (xtermRef.current) {
                xtermRef.current.writeln(
                    "\x1b[31m❌ Failed to connect to backend\x1b[0m"
                );
            }
        }
    };

    const handleLogMessage = (data: any) => {
        if (!xtermRef.current) return;

        const timestamp = new Date().toLocaleTimeString();
        let formattedMessage = "";

        if (data.type === "log") {
            const level = data.level?.toUpperCase() || "INFO";
            const message = data.message || "";
            const stepId = data.step_id || data.module || "system";

            // Color coding based on log level
            let colorCode = "\x1b[37m"; // white
            if (level === "ERROR") colorCode = "\x1b[31m"; // red
            else if (level === "SUCCESS") colorCode = "\x1b[32m"; // green
            else if (level === "WARNING" || level === "WARN")
                colorCode = "\x1b[33m"; // yellow
            else if (level === "INFO") colorCode = "\x1b[36m"; // cyan
            else if (level === "DEBUG") colorCode = "\x1b[90m"; // gray

            formattedMessage = `\x1b[90m[${timestamp}]\x1b[0m ${colorCode}[${level}]\x1b[0m \x1b[35m[${stepId}]\x1b[0m ${message}`;
        } else if (data.type === "status") {
            formattedMessage = `\x1b[90m[${timestamp}]\x1b[0m \x1b[1;34m[STATUS]\x1b[0m ${
                data.message || data.status || ""
            }`;
        } else if (data.type === "pipeline_update") {
            const step = data.step || "unknown";
            const status = data.status || "unknown";
            const progress = data.progress ? ` (${data.progress}%)` : "";
            formattedMessage = `\x1b[90m[${timestamp}]\x1b[0m \x1b[1;35m[PIPELINE]\x1b[0m ${step}: ${status}${progress}`;
        } else {
            // Generic message
            formattedMessage = `\x1b[90m[${timestamp}]\x1b[0m ${JSON.stringify(
                data
            )}`;
        }

        xtermRef.current.writeln(formattedMessage);
        xtermRef.current.scrollToBottom();

        // Update log count and buffer
        logBuffer.current.push(formattedMessage);
        setLogCount((prev) => prev + 1);

        // Keep buffer size manageable
        if (logBuffer.current.length > 1000) {
            logBuffer.current = logBuffer.current.slice(-1000);
        }
    };

    const handlePlainTextMessage = (message: string) => {
        if (!xtermRef.current) return;

        const timestamp = new Date().toLocaleTimeString();
        const formattedMessage = `\x1b[90m[${timestamp}]\x1b[0m ${message}`;

        xtermRef.current.writeln(formattedMessage);
        xtermRef.current.scrollToBottom();

        logBuffer.current.push(formattedMessage);
        setLogCount((prev) => prev + 1);
    };

    const handleClear = () => {
        if (xtermRef.current) {
            xtermRef.current.clear();
            logBuffer.current = [];
            setLogCount(0);

            // Re-show header
            xtermRef.current.writeln(
                "\x1b[1;36m╭─────────────────────────────────────────────────────────────────╮\x1b[0m"
            );
            xtermRef.current.writeln(
                "\x1b[1;36m│  🔴 LIVE TERMINAL - Real-time Backend Logs                     │\x1b[0m"
            );
            xtermRef.current.writeln(
                "\x1b[1;36m╰─────────────────────────────────────────────────────────────────╯\x1b[0m"
            );
            xtermRef.current.writeln("");
            xtermRef.current.writeln("\x1b[36m✨ Terminal cleared\x1b[0m");
            xtermRef.current.writeln("");
        }
    };

    const handleCopy = async () => {
        if (xtermRef.current) {
            const selection = xtermRef.current.getSelection();
            if (selection) {
                await navigator.clipboard.writeText(selection);
            }
        }
    };

    const handleSearch = (term: string) => {
        if (searchAddonRef.current && term) {
            searchAddonRef.current.findNext(term);
        }
    };

    const handleDownload = () => {
        const logs = logBuffer.current.join("\n");
        const blob = new Blob([logs], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `live-terminal-logs-${new Date()
            .toISOString()
            .slice(0, 19)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const getConnectionIcon = () => {
        switch (connectionStatus) {
            case "connected":
                return <Wifi className="w-4 h-4 text-green-500" />;
            case "connecting":
                return (
                    <Wifi className="w-4 h-4 text-yellow-500 animate-pulse" />
                );
            case "disconnected":
                return <WifiOff className="w-4 h-4 text-red-500" />;
        }
    };

    const getConnectionText = () => {
        switch (connectionStatus) {
            case "connected":
                return "Live";
            case "connecting":
                return "Connecting";
            case "disconnected":
                return "Disconnected";
        }
    };

    return (
        <div
            className={`flex flex-col ${
                isFullscreen ? "fixed inset-0 z-50 bg-gray-900" : className
            }`}
            style={{
                height: isFullscreen
                    ? "100vh"
                    : height
                    ? `${currentHeight}px`
                    : "100%",
            }}
        >
            {/* Terminal Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-700 bg-gray-800">
                <div className="flex items-center space-x-3">
                    <div className="flex space-x-1">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <span className="text-sm text-white font-medium">
                        Live Terminal
                    </span>
                    <div className="flex items-center space-x-2">
                        {getConnectionIcon()}
                        <span
                            className={`text-xs px-2 py-1 rounded ${
                                connectionStatus === "connected"
                                    ? "bg-green-600 text-white"
                                    : connectionStatus === "connecting"
                                    ? "bg-yellow-600 text-white"
                                    : "bg-red-600 text-white"
                            }`}
                        >
                            {getConnectionText()}
                        </span>
                    </div>
                    <span className="text-xs text-gray-400">
                        {logCount} logs
                    </span>
                </div>

                <div className="flex items-center space-x-2">
                    {/* Search */}
                    <div className="relative">
                        <button
                            onClick={() => setSearchVisible(!searchVisible)}
                            className="p-1 text-gray-400 hover:text-white transition-colors"
                            title="Search"
                        >
                            <Search className="w-4 h-4" />
                        </button>
                        {searchVisible && (
                            <div className="absolute right-0 top-8 bg-gray-800 border border-gray-600 rounded p-2 z-10">
                                <input
                                    type="text"
                                    value={searchTerm}
                                    onChange={(e) =>
                                        setSearchTerm(e.target.value)
                                    }
                                    onKeyDown={(e) =>
                                        e.key === "Enter" &&
                                        handleSearch(searchTerm)
                                    }
                                    placeholder="Search..."
                                    className="w-32 px-2 py-1 text-xs bg-gray-700 text-white border border-gray-600 rounded"
                                    autoFocus
                                />
                            </div>
                        )}
                    </div>

                    <button
                        onClick={handleCopy}
                        className="p-1 text-gray-400 hover:text-white transition-colors"
                        title="Copy Selection"
                    >
                        <Copy className="w-4 h-4" />
                    </button>

                    <button
                        onClick={handleDownload}
                        className="p-1 text-gray-400 hover:text-white transition-colors"
                        title="Download Logs"
                    >
                        <Download className="w-4 h-4" />
                    </button>

                    {height && onResize && (
                        <button
                            onMouseDown={handleResizeStart}
                            className="p-1 text-gray-400 hover:text-white transition-colors cursor-ns-resize"
                            title="Resize Terminal"
                        >
                            <Move className="w-4 h-4" />
                        </button>
                    )}

                    <button
                        onClick={() => setIsFullscreen(!isFullscreen)}
                        className="p-1 text-gray-400 hover:text-white transition-colors"
                        title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
                    >
                        {isFullscreen ? (
                            <Minimize2 className="w-4 h-4" />
                        ) : (
                            <Maximize2 className="w-4 h-4" />
                        )}
                    </button>

                    <button
                        onClick={handleClear}
                        className="px-3 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors"
                    >
                        Clear
                    </button>
                </div>
            </div>

            {/* Terminal Content */}
            <div className="flex-1 overflow-hidden">
                <div
                    ref={terminalRef}
                    className="h-full w-full"
                    style={{ padding: "12px" }}
                />
            </div>

            {/* Resize Handle - only show when height prop is provided */}
            {!isFullscreen && height && onResize && (
                <div
                    className="h-1 bg-gray-600 hover:bg-gray-500 cursor-ns-resize transition-colors"
                    onMouseDown={handleResizeStart}
                    title="Drag to resize terminal"
                />
            )}
        </div>
    );
};
