/**
 * PHOENIX PROJECT: Pipeline Navigator
 * The SINGLE SOURCE OF TRUTH for file upload and pipeline execution.
 * Contains the file upload logic and Execute Step buttons.
 * Rebuilt from the ground up with absolute precision.
 */
import React, { useCallback } from "react";
import {
    Upload,
    FileText,
    Play,
    CheckCircle,
    XCircle,
    Clock,
    Wifi,
    WifiOff,
} from "lucide-react";
import {
    useCurrentFile,
    usePipelineRunId,
    useNodeStatuses,
    useAppActions,
    useIsConnected,
} from "../store/appStore";

// PHOENIX BLUEPRINT: Pipeline Configuration
const PIPELINE_STEPS = [
    { id: "document_processor", name: "Document Processor", number: 1 },
    { id: "business_flow_detector", name: "Business Flow Detector", number: 2 },
    {
        id: "relevant_content_processor",
        name: "Relevant Content Processor",
        number: 3,
    },
    { id: "path_finder", name: "Path Finder", number: 4 },
    { id: "path_to_csv", name: "Path to CSV", number: 5 },
    { id: "test_case_generator", name: "Test Case Generator", number: 6 },
    { id: "test_case_evaluator", name: "Test Case Evaluator", number: 7 },
];

// PHOENIX BLUEPRINT: Status Icons
const StatusIcon = ({ status }: { status: string }) => {
    switch (status) {
        case "running":
            return <Clock className="w-5 h-5 text-yellow-500 animate-spin" />;
        case "success":
            return <CheckCircle className="w-5 h-5 text-green-500" />;
        case "error":
            return <XCircle className="w-5 h-5 text-red-500" />;
        default:
            return (
                <div className="w-5 h-5 rounded-full border-2 border-gray-500" />
            );
    }
};

export const PipelineNavigator: React.FC = () => {
    const currentFile = useCurrentFile();
    const pipelineRunId = usePipelineRunId();
    const nodeStatuses = useNodeStatuses();
    const isConnected = useIsConnected();
    const {
        setCurrentFile,
        setPipelineRunId,
        setIsUploading,
        startExecution,
        setNodeStatus,
        clearLogs,
    } = useAppActions();

    // PHOENIX BLUEPRINT: File Upload Handler
    const handleFileUpload = useCallback(
        async (event: React.ChangeEvent<HTMLInputElement>) => {
            const file = event.target.files?.[0];
            if (!file) return;

            if (!file.name.toLowerCase().endsWith(".pdf")) {
                alert("Only PDF files are supported");
                return;
            }

            setIsUploading(true);

            try {
                const formData = new FormData();
                formData.append("file", file);

                const response = await fetch("/api/v1/upload-and-prepare", {
                    method: "POST",
                    body: formData,
                });

                if (!response.ok) {
                    throw new Error(`Upload failed: ${response.statusText}`);
                }

                const result = await response.json();

                // Store file info in global state
                setCurrentFile({
                    id: result.fileId,
                    name: result.fileName,
                });

                // Clear previous logs
                clearLogs();
            } catch (error) {
                console.error("Upload failed:", error);
                alert(
                    `Upload failed: ${
                        error instanceof Error ? error.message : "Unknown error"
                    }`
                );
            } finally {
                setIsUploading(false);
            }
        },
        [setCurrentFile, setIsUploading, clearLogs]
    );

    // PHOENIX BLUEPRINT: Execute Step Handler
    const handleExecuteStep = useCallback(
        async (stepId: string) => {
            if (!currentFile || !currentFile.id) {
                alert("Please upload a file first");
                return;
            }

            // Generate run ID if none exists
            let runId = pipelineRunId;
            if (!runId) {
                runId = `run_${Date.now()}_${Math.random()
                    .toString(36)
                    .substring(2, 11)}`;
                setPipelineRunId(runId);
            }

            // Start execution in global state (this sets status to 'running' and connects WebSocket)
            startExecution(stepId);

            try {
                // Wait a bit for WebSocket to connect
                await new Promise((resolve) => setTimeout(resolve, 200));

                console.log("🚀 Sending execute request:", {
                    stepId: stepId,
                    fileId: currentFile.id,
                    runId: runId,
                });

                const response = await fetch("/api/v1/execute-step", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        // Use unified execution format (camelCase)
                        stepId: stepId,
                        fileId: currentFile.id,
                        runId: runId,
                        parameters: {
                            skip_llm: false,
                            count_token: true,
                        },
                    }),
                });

                console.log(
                    "📡 Response status:",
                    response.status,
                    response.statusText
                );

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error("❌ Backend error response:", errorText);
                    throw new Error(`Execution failed: ${response.statusText}`);
                }

                const result = await response.json();
                console.log("✅ Backend response:", result);

                // Note: Status will be updated via WebSocket when step completes
                // The backend sends step_update messages with 'success' or 'error' status

                // Fallback: Poll for status updates if WebSocket fails
                setTimeout(() => {
                    console.log(
                        "🔍 Checking if step is still running after 30 seconds..."
                    );
                    const currentStatus = nodeStatuses[stepId];
                    if (currentStatus === "running") {
                        console.log(
                            "⚠️ Step still running after 30s, WebSocket might not be working"
                        );
                        console.log(
                            "💡 Consider checking WebSocket connection in browser dev tools"
                        );
                        console.log(
                            "🔍 Current WebSocket status:",
                            isConnected ? "Connected" : "Disconnected"
                        );
                        console.log("🔍 Current run ID:", runId);
                    }
                }, 60000);
            } catch (error) {
                console.error("Execution failed:", error);
                setNodeStatus(stepId, "error");
                alert(
                    `Execution failed: ${
                        error instanceof Error ? error.message : "Unknown error"
                    }`
                );
            }
        },
        [
            currentFile,
            pipelineRunId,
            setPipelineRunId,
            startExecution,
            setNodeStatus,
            nodeStatuses,
        ]
    );

    return (
        <div className="h-full flex flex-col bg-gray-800">
            {/* PHOENIX BLUEPRINT: Header */}
            <div className="p-4 border-b border-gray-700">
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-xl font-bold text-white">
                            Pipeline Navigator
                        </h2>
                        <p className="text-gray-400 text-sm">
                            Upload PDF and execute pipeline steps
                        </p>
                    </div>
                    {/* WebSocket Status Indicator */}
                    <div className="flex items-center gap-2">
                        {isConnected ? (
                            <div className="flex items-center gap-1 text-green-400 text-xs">
                                <Wifi className="w-4 h-4" />
                                <span>Connected</span>
                            </div>
                        ) : (
                            <div className="flex items-center gap-1 text-gray-500 text-xs">
                                <WifiOff className="w-4 h-4" />
                                <span>Disconnected</span>
                            </div>
                        )}
                        {pipelineRunId && (
                            <div className="text-xs text-gray-500">
                                Run: {pipelineRunId.slice(-8)}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* PHOENIX BLUEPRINT: File Upload Section */}
            <div className="p-4 border-b border-gray-700">
                {!currentFile ? (
                    <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center hover:border-gray-500 transition-colors">
                        <Upload className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                        <p className="text-gray-400 mb-4">
                            Upload PDF Document
                        </p>
                        <label className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded cursor-pointer inline-block transition-colors">
                            Choose File
                            <input
                                type="file"
                                accept=".pdf"
                                onChange={handleFileUpload}
                                className="hidden"
                            />
                        </label>
                    </div>
                ) : (
                    <div className="bg-green-900/20 border border-green-700 rounded-lg p-4">
                        <div className="flex items-center gap-3">
                            <FileText className="w-6 h-6 text-green-500" />
                            <div>
                                <p className="text-green-400 font-medium">
                                    ✅ File ready: {currentFile.name}
                                </p>
                                <p className="text-gray-400 text-sm">
                                    ID: {currentFile.id}
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* PHOENIX BLUEPRINT: Pipeline Steps */}
            <div className="flex-1 overflow-y-auto p-4">
                <div className="space-y-3">
                    {PIPELINE_STEPS.map((step) => {
                        const status = nodeStatuses[step.id];
                        const isDisabled = !currentFile;

                        return (
                            <div
                                key={step.id}
                                className={`
                  border rounded-lg p-4 transition-all
                  ${
                      status === "running"
                          ? "border-yellow-500 bg-yellow-900/20"
                          : ""
                  }
                  ${
                      status === "success"
                          ? "border-green-500 bg-green-900/20"
                          : ""
                  }
                  ${status === "error" ? "border-red-500 bg-red-900/20" : ""}
                  ${status === "idle" ? "border-gray-600 bg-gray-800/50" : ""}
                `}
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-700 text-white text-sm font-bold">
                                            {step.number}
                                        </div>
                                        <div>
                                            <h3 className="font-medium text-white">
                                                {step.name}
                                            </h3>
                                            <p className="text-gray-400 text-sm">
                                                Step {step.number}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3">
                                        <StatusIcon status={status} />
                                        <button
                                            onClick={() =>
                                                handleExecuteStep(step.id)
                                            }
                                            disabled={
                                                isDisabled ||
                                                status === "running"
                                            }
                                            className={`
                        flex items-center gap-2 px-4 py-2 rounded font-medium transition-colors
                        ${
                            isDisabled || status === "running"
                                ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                                : "bg-blue-600 hover:bg-blue-700 text-white"
                        }
                      `}
                                        >
                                            <Play className="w-4 h-4" />
                                            Execute Step
                                        </button>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};
