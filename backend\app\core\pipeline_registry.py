"""
Robust Pipeline Registry - Maps Config IDs to Actual Functions
This eliminates mismatches between configuration and implementation
"""
import logging
from typing import Dict, Callable, Any, Optional
from .decorators import PIPELINE_STEPS_REGISTRY, get_pipeline_steps, get_pipeline_step

logger = logging.getLogger(__name__)

# Import all pipeline functions
try:
    from pipelines.real_pipelines import (
        process_document,
        generate_business_flows,
        process_relevant_content,
        generate_screen_paths,
        convert_paths_to_csv,
        generate_test_cases,
        evaluate_test_cases
    )
    logger.info("✅ Successfully imported all pipeline functions")
except ImportError as e:
    logger.error(f"❌ Failed to import pipeline functions: {e}")
    # Create dummy functions to prevent crashes
    import_error_msg = str(e)  # Capture the error message
    async def dummy_function(*args, **kwargs):
        return {"status": "error", "message": f"Function not implemented: {import_error_msg}"}

    process_document = dummy_function
    generate_business_flows = dummy_function
    process_relevant_content = dummy_function
    generate_screen_paths = dummy_function
    convert_paths_to_csv = dummy_function
    generate_test_cases = dummy_function
    evaluate_test_cases = dummy_function

# DEFINITIVE PIPELINE REGISTRY - Maps Config IDs to Real Functions
PIPELINE_REGISTRY: Dict[str, Callable] = {
    # Config ID -> Actual Function Object
    "document_processor": process_document,
    "extract_and_process_to_json": process_document,  # TEMP: Use document processor until image extractor is implemented
    "business_flow_detector": generate_business_flows,
    "relevant_content_processor": process_relevant_content,
    "path_finder": generate_screen_paths,
    "path_to_csv": convert_paths_to_csv,
    "gen_test_case": generate_test_cases,
    "test_case_evaluator": evaluate_test_cases
}

# Dependency configuration for fail-fast architecture
PIPELINE_DEPENDENCIES = {
    "document_processor": [],  # No dependencies - entry point
    "extract_and_process_to_json": [],  # No dependencies - entry point
    "business_flow_detector": ["document_processor"],
    "relevant_content_processor": ["document_processor", "business_flow_detector"],
    "path_finder": ["relevant_content_processor"],
    "path_to_csv": ["path_finder"],
    "gen_test_case": ["path_to_csv"],
    "test_case_evaluator": ["gen_test_case"]
}

# Required input files for each pipeline step
REQUIRED_INPUT_FILES = {
    "document_processor": [],  # Uses uploaded PDF file
    "extract_and_process_to_json": [],  # Uses uploaded PDF file
    "business_flow_detector": ["merged_output.json"],
    "relevant_content_processor": ["merged_output.json", "business_flows/"],
    "path_finder": ["screen_graph.json", "relevant_content/"],
    "path_to_csv": ["screen_paths.json"],
    "gen_test_case": ["test_paths.csv"],
    "test_case_evaluator": ["test_cases.json"]
}

def get_pipeline_function(step_id: str) -> Optional[Callable]:
    """
    Get the actual function for a pipeline step ID.

    Args:
        step_id: The pipeline step identifier from config

    Returns:
        The callable function or None if not found
    """
    return PIPELINE_REGISTRY.get(step_id)

async def execute_pipeline_step(step_id: str, **kwargs) -> Dict[str, Any]:
    """
    Execute a pipeline step by ID with given parameters.
    All pipeline functions now follow the standardized signature: (params: dict, logger, **kwargs)

    Args:
        step_id: The pipeline step identifier
        **kwargs: Parameters to pass to the function (must include input_path, output_dir, logger, run_id)

    Returns:
        Execution result
    """
    func = get_pipeline_function(step_id)
    if not func:
        return {
            "success": False,
            "error": f"Pipeline step '{step_id}' not found in registry",
            "step_id": step_id
        }

    try:
        logger.info(f"🚀 Executing pipeline step: {step_id}")

        # Extract required parameters
        pipeline_logger = kwargs.get("logger")
        run_id = kwargs.get("run_id")

        if not all([pipeline_logger, run_id]):
            missing = [k for k, v in {"logger": pipeline_logger, "run_id": run_id}.items() if not v]
            return {
                "success": False,
                "error": f"Missing required parameters: {missing}",
                "step_id": step_id
            }

        # Create params dictionary with all execution parameters (excluding logger)
        params = {k: v for k, v in kwargs.items() if k != "logger"}

        # Create clean kwargs without logger to avoid duplication
        clean_kwargs = {k: v for k, v in kwargs.items() if k != "logger"}
        clean_kwargs["step_id"] = step_id

        # Call the function with the new standardized signature
        result = await func(
            params=params,
            logger=pipeline_logger,
            **clean_kwargs
        )

        # Ensure result is a dictionary
        if not isinstance(result, dict):
            result = {"result": result}

        # Add success flag if not present
        if "success" not in result:
            result["success"] = result.get("status") == "success"

        result["step_id"] = step_id
        return result

    except Exception as e:
        logger.error(f"❌ Pipeline step '{step_id}' failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "step_id": step_id
        }

# Export the registry as pipeline_registry for backward compatibility
pipeline_registry = PIPELINE_STEPS_REGISTRY

# Export utility functions
__all__ = [
    'PIPELINE_REGISTRY', 'pipeline_registry', 'get_pipeline_steps', 'get_pipeline_step',
    'get_pipeline_function', 'execute_pipeline_step'
]
