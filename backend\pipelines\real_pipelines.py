"""
Real pipeline implementations using the user's existing classes from back_end folder
"""
import os
import sys
import json
import logging
import subprocess
import uuid

import asyncio
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

# Add the root directory to Python path
root_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(root_dir))

# Robust file reading utility to handle encoding issues
def safe_read_json(file_path: str, logger=None, run_id: str = None, step_id: str = None):
    """
    Safely read JSON file with automatic encoding detection and fallback strategies.
    This fixes the common issue where files written in notebook environment
    can't be read in web server environment due to encoding differences.
    """
    encodings_to_try = ['utf-8', 'cp1252', 'latin-1', 'utf-16']

    try:
        # Try each encoding until one works
        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = json.load(f)
                    if logger and run_id:
                        asyncio.create_task(logger.log(run_id, "INFO",
                            f"📖 Successfully read {os.path.basename(file_path)} with {encoding} encoding",
                            step_id))
                    return content
            except (UnicodeDecodeError, json.JSONDecodeError):
                continue

        # If all encodings fail, raise the last error
        raise UnicodeDecodeError(f"Could not decode {file_path} with any of {encodings_to_try}")

    except Exception as e:
        if logger and run_id:
            asyncio.create_task(logger.log(run_id, "ERROR",
                f"❌ Failed to read {os.path.basename(file_path)}: {e}",
                step_id))
        raise

def safe_write_json(file_path: str, data: Any, logger=None, run_id: str = None, step_id: str = None):
    """
    Safely write JSON file with consistent UTF-8 encoding.
    """
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        if logger and run_id:
            asyncio.create_task(logger.log(run_id, "INFO",
                f"💾 Successfully wrote {os.path.basename(file_path)} with UTF-8 encoding",
                step_id))
        return True
    except Exception as e:
        if logger and run_id:
            asyncio.create_task(logger.log(run_id, "ERROR",
                f"❌ Failed to write {os.path.basename(file_path)}: {e}",
                step_id))
        raise

# Import centralized log emitter for unified logging
try:
    from app.core.log_emitter import get_logger, get_sync_adapter
    from app.core.websocket_manager import connection_manager
except ImportError:
    # Fallback for when running outside FastAPI context
    connection_manager = None
    get_logger = None
    get_sync_adapter = None

# Helper function for real-time logging (DEPRECATED - use LogEmitter instead)
async def log_to_websocket(run_id: str, level: str, message: str, step_id: str, metadata: Optional[Dict[str, Any]] = None):
    """DEPRECATED: Use LogEmitter instead. Send log message to WebSocket clients if connection manager is available"""
    if connection_manager:
        try:
            await connection_manager.broadcast_log(run_id, {
                "level": level,
                "message": message,
                "step_id": step_id,
                "metadata": metadata or {}
            })
        except Exception as e:
            pass

    # Always log to standard logging as well
    log_level = getattr(logging, level.upper(), logging.INFO)
    logging.log(log_level, f"[{step_id}] {message}")

async def send_step_status(run_id: str, step_id: str, status: str, progress: Optional[float] = None):
    """Send step status update to WebSocket clients"""
    if connection_manager:
        try:
            await connection_manager.send_step_update(run_id, step_id, status, progress)
        except Exception as e:
            pass

# Import pipeline_step decorator
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

try:
    from app.core.decorators import pipeline_step
except ImportError:
    # Fallback to direct import
    import importlib.util
    spec = importlib.util.spec_from_file_location("decorators", backend_dir / "app" / "core" / "decorators.py")
    decorators_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(decorators_module)
    pipeline_step = decorators_module.pipeline_step

def run_user_script(script_path: str, args: list, cwd: str = None) -> Dict[str, Any]:
    """Run a user's Python script and return the result"""
    try:
        # Change to root directory to run the script
        if cwd is None:
            cwd = str(root_dir)

        # Run the script
        cmd = [sys.executable, script_path] + args

        result = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=600,  # 10 minute timeout
            encoding='utf-8',
            errors='replace'
        )

        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }

    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": "Script execution timed out after 5 minutes",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": str(e),
            "returncode": -1
        }

@pipeline_step(
    name="Document Processor",
    description="Processes PDF documents with interactive options and structured output",
    outputs=["document_structure.json", "merged_output.json", "analyzed_output.json"],
    inputs=["input_document.pdf"],
    configurable_params={
        "use_auto_toc": {
            "type": "boolean",
            "default": True,
            "description": "Automatically generate table of contents from PDF outline"
        },
        "count_token": {
            "type": "boolean",
            "default": True,
            "description": "Enable token counting and cost calculation"
        },
        "skip_llm": {
            "type": "boolean",
            "default": False,
            "description": "Skip LLM analysis to save tokens and time"
        },
        "output_format": {
            "type": "string",
            "default": "ecommerce",
            "description": "Output folder naming format (e.g., 'ecommerce-01-07')"
        }
    }
)
async def process_document(
    params: dict,
    logger,
    **kwargs
):
    """
    Process PDF document with interactive options and structured output.
    Uses centralized LogEmitter for unified logging to console and WebSocket.

    Args:
        params: Dictionary containing all execution parameters
        logger: LogEmitter instance for centralized logging
        **kwargs: Additional parameters from the central executor

    Returns:
        Dict containing status, message, and output files
    """
    # Extract parameters from the standardized structure
    input_path = kwargs.get("input_path") or params.get("input_path")
    output_dir = kwargs.get("output_dir") or params.get("output_dir")
    run_id = kwargs.get("run_id") or params.get("run_id")
    step_id = kwargs.get("step_id", "document_processor")

    # Extract processing parameters with defaults
    use_auto_toc = params.get("use_auto_toc", True)
    count_token = params.get("count_token", True)
    skip_llm = params.get("skip_llm", False)
    output_format = params.get("output_format", "ecommerce")

    try:
        # Create structured output directories
        os.makedirs(output_dir, exist_ok=True)
        document_processor_dir = os.path.join(output_dir, "document_processor")
        os.makedirs(document_processor_dir, exist_ok=True)

        # Define output folder name for logging
        output_folder_name = os.path.basename(output_dir)

        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))

        # Import and use the user's DocumentProcessor class directly
        try:
            from back_end.document_processor import DocumentProcessor
        except ImportError as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to import DocumentProcessor: {e}", step_id)
            return {
                "status": "error",
                "message": f"Failed to import DocumentProcessor: {e}",
                "error_details": str(e)
            }

        # Setup file paths in structured format
        merged_json_path = os.path.join(document_processor_dir, "merged_output.json")
        analyzed_json_path = os.path.join(document_processor_dir, "description_output.json")
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")

        # Initialize the user's DocumentProcessor
        try:
            processor = DocumentProcessor(
                pdf_path=input_path,
                config_file=config_file,
                output_dir=document_processor_dir,
                merged_json=merged_json_path,
                analyzed_json=analyzed_json_path,
                use_auto_toc=use_auto_toc,
                count_token=count_token,
                enable_caching=True
            )
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to initialize DocumentProcessor: {e}", step_id)
            return {
                "status": "error",
                "message": f"Failed to initialize DocumentProcessor: {e}",
                "error_details": str(e)
            }

        # Process the document using the execute method with live logging capture
        try:
            # Import log capture utilities
            from app.core.terminal_logger import LogCapture

            # Use context manager to capture DocumentProcessor's logs with 1s delay
            with LogCapture(run_id, step_id, ['back_end.document_processor', '__main__'], delay_seconds=1.0):
                # Execute the DocumentProcessor - now its logs will be captured and sent to terminal
                processor.execute(verbose=True, skip_llm=skip_llm)

        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Document processing failed: {e}", step_id)
            return {
                "status": "error",
                "message": f"Document processing failed: {e}",
                "error_details": str(e)
            }

        # Check output files and create structured summary
        output_files = []

        if os.path.exists(merged_json_path):
            output_files.append("merged_output.json")
        if os.path.exists(analyzed_json_path):
            output_files.append("description_output.json")

        # Create document_structure.json in main output directory
        document_structure_path = os.path.join(output_dir, "document_structure.json")
        if not os.path.exists(document_structure_path) and os.path.exists(merged_json_path):
            try:
                with open(merged_json_path, 'r', encoding='utf-8') as f:
                    merged_data = json.load(f)

                # Defensive handling: merged_data might be a list of sections or a dict
                if isinstance(merged_data, list):
                    # If it's a list, it contains sections directly
                    sections = merged_data
                    pages = []
                    total_pages = 0
                elif isinstance(merged_data, dict):
                    # If it's a dict, extract sections and pages
                    sections = merged_data.get("sections", [])
                    pages = merged_data.get("pages", [])
                    total_pages = len(pages) if pages else 0
                else:
                    # Fallback for unexpected data types
                    sections = []
                    pages = []
                    total_pages = 0

                structure = {
                    "document_info": {
                        "source": input_path,
                        "output_folder": output_folder_name,
                        "processed_at": datetime.now().isoformat(),
                        "total_pages": total_pages,
                        "skip_llm": skip_llm
                    },
                    "sections": sections,
                    "pages": pages
                }

                with open(document_structure_path, 'w', encoding='utf-8') as f:
                    json.dump(structure, f, indent=2, ensure_ascii=False)
                output_files.append("document_structure.json")
            except Exception as e:
                pass

        return {
            "status": "success",
            "message": f"Document processed successfully - output saved to {output_folder_name}",
            "output_files": output_files,
            "output_dir": output_dir,
            "output_folder": output_folder_name,
            "use_auto_toc": use_auto_toc,
            "count_token": count_token,
            "skip_llm": skip_llm,
            "pages_processed": total_pages if "total_pages" in locals() else "unknown"
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Document processing failed: {str(e)}",
            "error_details": str(e)
        }

@pipeline_step(
    name="Business Flow Generator",
    description="Generates business flows using the user's BusinessFlowDetector.py script",
    outputs=["business_flows/", "business_flow_summary.json"],
    inputs=["document_structure.json"],
    configurable_params={
        "max_flows": {
            "type": "number",
            "default": 15,
            "min": 1,
            "max": 50,
            "description": "Maximum number of business flows to generate"
        },
        "count_token": {
            "type": "boolean",
            "default": True,
            "description": "Enable token counting and cost calculation"
        }
    }
)
async def generate_business_flows(
    params: dict,
    logger,
    **kwargs
):
    """Generate business flows using the user's GenBusinessFlow class directly"""
    # Extract parameters from the standardized structure
    input_path = kwargs.get("input_path") or params.get("input_path")
    output_dir = kwargs.get("output_dir") or params.get("output_dir")
    run_id = kwargs.get("run_id") or params.get("run_id")
    step_id = kwargs.get("step_id", "business_flow_detector")

    # Extract processing parameters with defaults
    max_flows = params.get("max_flows", 15)
    count_token = params.get("count_token", True)

    try:
        # Setup paths
        business_flows_dir = os.path.join(output_dir, "business_flows")
        os.makedirs(business_flows_dir, exist_ok=True)

        # Find the merged JSON file from document processor
        merged_json = os.path.join(output_dir, "merged_output.json")
        if not os.path.exists(merged_json):
            # Try to find the merged JSON in the document_processor subdirectory
            doc_processor_merged = os.path.join(output_dir, "document_processor", "merged_output.json")
            if os.path.exists(doc_processor_merged):
                merged_json = doc_processor_merged
            else:
                # Check for description_output.json as alternative
                description_json = os.path.join(output_dir, "document_processor", "description_output.json")
                if os.path.exists(description_json):
                    merged_json = description_json
                else:
                    raise FileNotFoundError(f"Required input JSON file not found. Checked: {merged_json}, {doc_processor_merged}, {description_json}")

        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))

        # Import and use the user's GenBusinessFlow class directly
        try:
            from back_end.BusinessFlowDetector import GenBusinessFlow
        except ImportError as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to import GenBusinessFlow: {e}", step_id)
            return {
                "status": "error",
                "message": f"Failed to import GenBusinessFlow: {e}",
                "error_details": str(e)
            }

        # Setup file paths
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")

        # Initialize the user's GenBusinessFlow
        try:
            detector = GenBusinessFlow(
                input_json_file=merged_json,
                business_flow_dir=business_flows_dir,
                config_file=config_file,
                count_token=count_token
            )
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to initialize GenBusinessFlow: {e}", step_id)
            return {
                "status": "error",
                "message": f"Failed to initialize GenBusinessFlow: {e}",
                "error_details": str(e)
            }

        # Generate business flows with live logging
        try:
            # Import log capture utilities
            from app.core.terminal_logger import LogCapture

            # Use context manager to capture BusinessFlowDetector's logs with 1s delay
            with LogCapture(run_id, step_id, ['back_end.BusinessFlowDetector', '__main__'], delay_seconds=1.0):
                result = detector.execute()

        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Business flow generation failed: {e}", step_id)
            return {
                "status": "error",
                "message": f"Business flow generation failed: {e}",
                "error_details": str(e)
            }

        # Check results
        business_flow_files = []
        if os.path.exists(business_flows_dir):
            business_flow_files = [f for f in os.listdir(business_flows_dir) if f.endswith('.txt')]

        flows_generated = len(business_flow_files)

        # Create summary
        summary = {
            "total_flows_generated": flows_generated,
            "output_directory": business_flows_dir,
            "source_document": merged_json,
            "max_flows_requested": max_flows,
            "business_flow_files": business_flow_files,
            "generated_flows": result if result else []
        }

        summary_path = os.path.join(output_dir, "business_flow_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        return {
            "status": "success",
            "message": f"Successfully generated {flows_generated} business flows using user's GenBusinessFlow class",
            "flows_generated": flows_generated,
            "output_directory": business_flows_dir,
            "summary_file": summary_path,
            "business_flow_files": business_flow_files,
            "generated_flows": result if result else []
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Business flow generation failed: {str(e)}",
            "error_details": str(e)
        }

@pipeline_step(
    name="Relevant Content Processor",
    description="Processes business flows to extract relevant content using the user's RelevantContentProcessor class",
    outputs=["relevant_content_processor/", "screen_graph.json", "merged_relevant_content.json"],
    inputs=["business_flows/", "document_structure.json"],
    configurable_params={
        "max_workers": {
            "type": "number",
            "default": 3,
            "min": 1,
            "max": 10,
            "description": "Maximum number of parallel workers"
        },
        "count_token": {
            "type": "boolean",
            "default": True,
            "description": "Enable token counting and cost calculation"
        },
        "enable_caching": {
            "type": "boolean",
            "default": True,
            "description": "Enable caching for relevant content"
        }
    }
)
async def process_relevant_content(
    params: dict,
    logger,
    context: dict = None,
    **kwargs
):
    """
    Process relevant content using execution context (new) or legacy parameters (old).
    CONVENTION OVER CONFIGURATION: When context is provided, all paths are guaranteed to exist.
    """
    step_id = "relevant_content_processor"

    # NEW: Context-based execution (eliminates None path errors)
    if context is not None:
        # Extract execution context - all paths are guaranteed to exist
        run_id = context["run_id"]

        # Get my output directory from context (guaranteed to exist)
        my_output_dir = context["step_outputs"][step_id]

        # Get dependency input directories from context (guaranteed to exist)
        document_processor_dir = context["step_outputs"]["document_processor"]
        business_flow_detector_dir = context["step_outputs"]["business_flow_detector"]

        # Get key file paths from context (guaranteed to be defined)
        merged_json = context["key_files"]["merged_output"]
        business_flows_dir = context["key_files"]["business_flows_dir"]
        await logger.log(run_id, "INFO", f"📁 My output directory: {my_output_dir}", step_id)
        await logger.log(run_id, "INFO", f"📥 Input from document_processor: {document_processor_dir}", step_id)
        await logger.log(run_id, "INFO", f"📥 Input from business_flow_detector: {business_flow_detector_dir}", step_id)

    # LEGACY: Parameter-based execution (backward compatibility)
    else:
        # Extract parameters from the standardized structure
        business_flows_path = kwargs.get("business_flows_path") or params.get("business_flows_path")
        document_structure_path = kwargs.get("document_structure_path") or params.get("document_structure_path")
        output_dir = kwargs.get("output_dir") or params.get("output_dir")
        run_id = kwargs.get("run_id") or params.get("run_id")

        # DEFENSIVE: Check for None values before path operations
        if output_dir is None:
            raise ValueError("output_dir cannot be None in legacy mode")
        if run_id is None:
            raise ValueError("run_id cannot be None")

        # Setup paths (legacy method)
        my_output_dir = os.path.join(output_dir, "relevant_content_processor")
        os.makedirs(my_output_dir, exist_ok=True)

        # Find merged JSON file (legacy method)
        merged_json = os.path.join(output_dir, "merged_output.json")
        if not os.path.exists(merged_json):
            if document_structure_path is None:
                raise ValueError("document_structure_path cannot be None when merged_output.json not found")
            merged_json = document_structure_path

        # Find business flows directory (legacy method)
        business_flows_dir = business_flows_path
        if business_flows_dir is None:
            business_flows_dir = os.path.join(output_dir, "business_flows")
        elif not os.path.isdir(business_flows_dir):
            business_flows_dir = os.path.join(output_dir, "business_flows")

    # Extract processing parameters with defaults
    max_workers = kwargs.get("max_workers", 3) or params.get("max_workers", 3)
    count_token = kwargs.get("count_token", True) or params.get("count_token", True)
    enable_caching = kwargs.get("enable_caching", True) or params.get("enable_caching", True)

    try:
        pass

        # Verify input files exist
        if not os.path.exists(merged_json):
            await logger.log(run_id, "ERROR", f"❌ CRITICAL: merged_output.json not found at: {merged_json}", step_id)
            raise FileNotFoundError(f"Required input file not found: {merged_json}")

        if not os.path.isdir(business_flows_dir):
            await logger.log(run_id, "ERROR", f"❌ CRITICAL: business_flows directory not found at: {business_flows_dir}", step_id)
            raise FileNotFoundError(f"Required input directory not found: {business_flows_dir}")

        await logger.log(run_id, "SUCCESS", "✅ All required input files verified", step_id)

        # Count business flow files
        try:
            flow_files = [f for f in os.listdir(business_flows_dir) if f.endswith('.txt')]
            await logger.log(run_id, "INFO", f"📊 FORENSICS: Found {len(flow_files)} business flow files to process: {flow_files}", step_id)
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to list business flow files: {e}", step_id)
            raise

        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))

        # Import and use the user's RelevantContentProcessor class directly

        try:
            # Try different import approaches
            try:
                from back_end.relevant_content_processor import RelevantContentProcessor
                await logger.log(run_id, "SUCCESS", "✅ Direct import successful", step_id)
            except ImportError as import_error:
                await logger.log(run_id, "WARNING", f"⚠️ Direct import failed: {import_error}, trying fallback import", step_id)
                # Fallback to direct import
                import importlib.util
                spec_path = os.path.join(root_dir, "back_end", "relevant_content_processor.py")

                spec = importlib.util.spec_from_file_location(
                    "relevant_content_processor",
                    spec_path
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                RelevantContentProcessor = module.RelevantContentProcessor
                await logger.log(run_id, "SUCCESS", "✅ Fallback import successful", step_id)

            await logger.log(run_id, "SUCCESS", "✅ RelevantContentProcessor class imported successfully", step_id)
        except Exception as e:
            error_msg = f"Failed to import RelevantContentProcessor: {e}"
            await logger.log(run_id, "ERROR", f"❌ CRITICAL FAILURE: {error_msg}", step_id)
            return {
                "status": "error",
                "message": error_msg,
                "error_details": str(e)
            }

        # Configure paths for the user's class
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")

        # DEFENSIVE: Validate config file path
        if not os.path.exists(config_file):
            await logger.log(run_id, "WARNING", f"⚠️ Config file not found: {config_file}", step_id)
            # Use a fallback or create a minimal config
            config_file = None

        # Initialize the user's RelevantContentProcessor
        await logger.log(run_id, "INFO", "🏗️ CONTEXT-BASED: Initializing RelevantContentProcessor with context paths...", step_id)

        try:
            processor = RelevantContentProcessor(
                description_json=merged_json,  # Use merged_json for both description and merged
                merged_json=merged_json,
                config_file=config_file,
                business_flows_dir=business_flows_dir,
                base_output_dir=my_output_dir,
                count_token=count_token,
                max_workers=max_workers,
                enable_caching=enable_caching
            )
        except Exception as e:
            error_msg = f"Failed to initialize RelevantContentProcessor: {e}"
            await logger.log(run_id, "ERROR", f"❌ CRITICAL FAILURE in initialization: {error_msg}", step_id)
            await logger.log(run_id, "ERROR", f"🔍 Exception details: {type(e).__name__}: {str(e)}", step_id)
            return {
                "status": "error",
                "message": error_msg,
                "error_details": str(e)
            }

        # Process relevant content with comprehensive error tracking

        try:
            # Get business flow files before processing
            try:
                business_flow_files = processor._get_business_flow_files()
            except Exception as e:
                await logger.log(run_id, "ERROR", f"❌ CRITICAL: Failed to get business flow files: {e}", step_id)
                raise

            # Execute all flows with live logging capture
            await logger.log(run_id, "INFO", "🔄 FORENSICS: Calling processor.execute_all_flows() with live logging...", step_id)

            # Import log capture utilities
            from app.core.terminal_logger import LogCapture

            # Use context manager to capture relevant_content_processor's logs with 1s delay
            with LogCapture(run_id, step_id, ['back_end.relevant_content_processor', '__main__'], delay_seconds=1.0):
                processor.execute_all_flows(rotate_api_key=False, use_threading=True)

            await logger.log(run_id, "SUCCESS", "✅ execute_all_flows() completed without exceptions", step_id)

            # Verify processing results
            try:
                result = {"flows_processed": len(business_flow_files)}
                await logger.log(run_id, "SUCCESS", f"✅ Relevant content processing completed successfully. Processed {result['flows_processed']} flows", step_id)
            except Exception as e:
                await logger.log(run_id, "WARNING", f"⚠️ Could not determine flows processed count: {e}", step_id)
                result = {"flows_processed": 0}

        except Exception as e:
            error_msg = f"Relevant content processing failed: {e}"
            await logger.log(run_id, "ERROR", f"❌ CRITICAL FAILURE in Relevant Content Processor: {error_msg}", step_id)
            await logger.log(run_id, "ERROR", f"🔍 Exception type: {type(e).__name__}", step_id)
            await logger.log(run_id, "ERROR", f"🔍 Exception details: {str(e)}", step_id)

            # Try to get more context about the failure
            try:
                import traceback
                tb_str = traceback.format_exc()
                await logger.log(run_id, "ERROR", f"🔍 Full traceback: {tb_str}", step_id)
            except:
                pass

            return {
                "status": "error",
                "message": error_msg,
                "error_details": str(e),
                "exception_type": type(e).__name__
            }

        # Generate screen graph with detailed logging
        screen_graph_path = context["key_files"]["screen_graph"]
        await logger.log(run_id, "INFO", "🎯 FORENSICS: Generating screen graph...", step_id)

        try:
            # Use the generate_shared_screen_graph method
            if hasattr(processor, 'generate_shared_screen_graph'):
                success = processor.generate_shared_screen_graph(rotate_api_key=False)
                if success:
                    await logger.log(run_id, "SUCCESS", "✅ Screen graph generated successfully", step_id)
                else:
                    await logger.log(run_id, "WARNING", "⚠️ Screen graph generation returned False", step_id)
            else:
                await logger.log(run_id, "WARNING", "⚠️ generate_shared_screen_graph method not found in RelevantContentProcessor", step_id)
        except Exception as e:
            await logger.log(run_id, "WARNING", f"⚠️ Failed to generate screen graph: {e}", step_id)

        # Final success return
        await logger.log(run_id, "SUCCESS", "🎉 Relevant Content Processing completed successfully!", step_id)
        return {
            "status": "success",
            "message": "Successfully processed relevant content using context-based approach",
            "output_directory": my_output_dir,
            "screen_graph_file": screen_graph_path if os.path.exists(screen_graph_path) else None,
            "business_flows_processed": result.get("flows_processed", 0) if result else 0,
            "max_workers": max_workers,
            "caching_enabled": enable_caching,
            "result": result if result else {}
        }

    except Exception as e:
        # Final catch-all exception handler with comprehensive logging
        error_msg = f"Unexpected error in relevant content processing: {str(e)}"
        await logger.log(run_id, "ERROR", f"❌ CRITICAL FAILURE in process_relevant_content: {error_msg}", step_id)
        await logger.log(run_id, "ERROR", f"🔍 Exception type: {type(e).__name__}", step_id)

        try:
            import traceback
            tb_str = traceback.format_exc()
            await logger.log(run_id, "ERROR", f"🔍 Full traceback: {tb_str}", step_id)
        except:
            pass

        return {
            "status": "error",
            "message": error_msg,
            "error_details": str(e),
            "exception_type": type(e).__name__
        }

@pipeline_step(
    name="Screen Path Generator",
    description="Generates screen navigation paths using the user's ScreenPathGenerator class",
    outputs=["path_processor/", "screen_variables.json", "screen_flow_graph.png"],
    inputs=["relevant_content_processor/", "screen_graph.json"],
    configurable_params={
        "max_path_depth": {
            "type": "number",
            "default": 5,
            "min": 2,
            "max": 15,
            "description": "Maximum depth for navigation paths"
        },
        "count_token": {
            "type": "boolean",
            "default": True,
            "description": "Enable token counting and cost calculation"
        }
    }
)
async def generate_screen_paths(
    params: dict,
    logger,
    **kwargs
):
    """Generate screen paths using the user's ScreenPathGenerator class"""
    # Extract parameters from the standardized structure
    relevant_content_path = kwargs.get("relevant_content_path") or params.get("relevant_content_path")
    screen_graph_path = kwargs.get("screen_graph_path") or params.get("screen_graph_path")
    output_dir = kwargs.get("output_dir") or params.get("output_dir")
    run_id = kwargs.get("run_id") or params.get("run_id")
    step_id = kwargs.get("step_id", "path_finder")

    # Extract processing parameters with defaults
    max_path_depth = params.get("max_path_depth", 5)
    count_token = params.get("count_token", True)

    try:
        # Setup paths
        path_processor_dir = os.path.join(output_dir, "path_processor")
        os.makedirs(path_processor_dir, exist_ok=True)

        # Find required input files
        relevant_content_dir = relevant_content_path
        if not os.path.isdir(relevant_content_dir):
            relevant_content_dir = os.path.join(output_dir, "relevant_content_processor")

        business_flows_dir = os.path.join(output_dir, "business_flows")

        if not os.path.exists(relevant_content_dir):
            raise FileNotFoundError(f"Relevant content directory not found: {relevant_content_dir}")

        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))

        # Import and use the user's ScreenPathGenerator class directly

        try:
            # Try different import approaches
            try:
                from back_end.path_processor import ScreenPathGenerator
            except ImportError:
                # Fallback to direct import
                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "path_processor",
                    os.path.join(root_dir, "back_end", "path_processor.py")
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                ScreenPathGenerator = module.ScreenPathGenerator
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to import ScreenPathGenerator: {e}",
                "error_details": str(e)
            }

        # Configure paths for the user's class
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")

        # Process each business flow
        business_flow_files = [f for f in os.listdir(business_flows_dir) if f.startswith("Business Flow") and f.endswith(".txt")]
        results = []

        for i, flow_file in enumerate(business_flow_files, 1):
            try:
                pass

                # Initialize the user's ScreenPathGenerator for each business flow
                flows_output_path = os.path.join(path_processor_dir, f"business_flow_{i}")
                screen_paths_output_path = os.path.join(path_processor_dir, f"screen_paths_{i}")

                generator = ScreenPathGenerator(
                    config_file=config_file,
                    relevant_content_base_dir=relevant_content_dir,
                    business_flows_dir=business_flows_dir,
                    business_flow_number=i,
                    flows_output_path=flows_output_path,
                    screen_paths_output_path=screen_paths_output_path,
                    base_output_dir=output_dir,
                    count_token=count_token
                )

                # Generate screen paths using execute method
                result = generator.execute()
                results.append({
                    "business_flow": i,
                    "result": result,
                    "flows_output": flows_output_path,
                    "screen_paths_output": screen_paths_output_path
                })

            except Exception as e:
                results.append({
                    "business_flow": i,
                    "error": str(e)
                })

        # Generate screen variables summary
        screen_variables_path = os.path.join(output_dir, "screen_variables.json")
        variables_summary = {
            "total_business_flows": len(business_flow_files),
            "processed_flows": len([r for r in results if "error" not in r]),
            "failed_flows": len([r for r in results if "error" in r]),
            "max_path_depth": max_path_depth,
            "results": results
        }

        with open(screen_variables_path, 'w', encoding='utf-8') as f:
            json.dump(variables_summary, f, indent=2, ensure_ascii=False)

        return {
            "status": "success",
            "message": "Screen paths generated successfully using user's ScreenPathGenerator class",
            "output_directory": path_processor_dir,
            "screen_variables_file": screen_variables_path,
            "business_flows_processed": len([r for r in results if "error" not in r]),
            "total_flows": len(business_flow_files),
            "max_path_depth": max_path_depth
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Screen path generation failed: {str(e)}",
            "error_details": str(e)
        }

@pipeline_step(
    name="Path to CSV Converter",
    description="Converts screen paths to CSV format using the user's ScreenTestCaseGenerator class",
    outputs=["test_case_csv_generator/", "csv_conversion_summary.json"],
    inputs=["path_processor/", "screen_variables.json"],
    configurable_params={
        "include_all_combinations": {
            "type": "boolean",
            "default": True,
            "description": "Include all possible test data combinations"
        },
        "max_combinations_per_flow": {
            "type": "number",
            "default": 50,
            "min": 10,
            "max": 200,
            "description": "Maximum combinations per business flow"
        }
    }
)
async def convert_paths_to_csv(
    params: dict,
    logger,
    **kwargs
):
    """Convert screen paths to CSV format using the user's ScreenTestCaseGenerator class"""
    # Extract parameters from the standardized structure
    path_processor_path = kwargs.get("path_processor_path") or params.get("path_processor_path")
    screen_variables_path = kwargs.get("screen_variables_path") or params.get("screen_variables_path")
    output_dir = kwargs.get("output_dir") or params.get("output_dir")
    run_id = kwargs.get("run_id") or params.get("run_id")
    step_id = kwargs.get("step_id", "path_to_csv")

    # Extract processing parameters with defaults
    include_all_combinations = params.get("include_all_combinations", True)
    max_combinations_per_flow = params.get("max_combinations_per_flow", 50)

    try:
        # Setup paths
        csv_output_dir = os.path.join(output_dir, "test_case_csv_generator")
        os.makedirs(csv_output_dir, exist_ok=True)

        # Find required input files
        path_processor_dir = path_processor_path
        if not os.path.isdir(path_processor_dir):
            path_processor_dir = os.path.join(output_dir, "path_processor")

        if not os.path.exists(path_processor_dir):
            raise FileNotFoundError(f"Path processor directory not found: {path_processor_dir}")

        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))

        # Import and use the user's ScreenTestCaseGenerator class directly

        try:
            # Try different import approaches
            try:
                from back_end.path_to_csv import ScreenTestCaseGenerator
            except ImportError:
                # Fallback to direct import
                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "path_to_csv",
                    os.path.join(root_dir, "back_end", "path_to_csv.py")
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                ScreenTestCaseGenerator = module.ScreenTestCaseGenerator
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to import ScreenTestCaseGenerator: {e}",
                "error_details": str(e)
            }

        # Process from the base directory structure

        try:
            # Use the process_from_base_dir method for new structure
            result = ScreenTestCaseGenerator.process_from_base_dir(
                base_dir=output_dir,
                output_dir=csv_output_dir
            )
        except Exception as e:
            return {
                "status": "error",
                "message": f"Path to CSV conversion failed: {e}",
                "error_details": str(e)
            }

        # Handle result (which is a list from process_from_base_dir)
        if isinstance(result, list):
            total_csv_files = sum(r.get("csv_files_generated", 0) for r in result if isinstance(r, dict))
            csv_files = []
            for r in result:
                if isinstance(r, dict) and "output_directory" in r:
                    csv_files.append(r["output_directory"])
        else:
            total_csv_files = result.get("csv_files_generated", 0) if result else 0
            csv_files = result.get("csv_files", []) if result else []

        # Create summary
        summary = {
            "total_csv_files": total_csv_files,
            "output_directory": csv_output_dir,
            "include_all_combinations": include_all_combinations,
            "max_combinations_per_flow": max_combinations_per_flow,
            "conversion_timestamp": None,
            "csv_files": csv_files,
            "business_flows_processed": len(result) if isinstance(result, list) else 1,
            "results": result if isinstance(result, list) else [result]
        }

        summary_path = os.path.join(output_dir, "csv_conversion_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        return {
            "status": "success",
            "message": f"Successfully converted screen paths to CSV for {len(result) if isinstance(result, list) else 1} business flows using user's ScreenTestCaseGenerator class",
            "output_directory": csv_output_dir,
            "summary_file": summary_path,
            "total_csv_files": total_csv_files,
            "business_flows_processed": len(result) if isinstance(result, list) else 1,
            "include_all_combinations": include_all_combinations,
            "max_combinations_per_flow": max_combinations_per_flow
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Path to CSV conversion failed: {str(e)}",
            "error_details": str(e)
        }

@pipeline_step(
    name="Test Case Generator",
    description="Generates test cases using the user's GenTestCaseBussinessFlow class",
    outputs=["final_test_cases/", "test_cases_summary.json"],
    inputs=["test_case_csv_generator/", "csv_conversion_summary.json"],
    configurable_params={
        "include_negative_tests": {
            "type": "boolean",
            "default": True,
            "description": "Include negative test cases"
        },
        "test_data_variants": {
            "type": "number",
            "default": 3,
            "min": 1,
            "max": 10,
            "description": "Number of test data variants per test case"
        }
    }
)
async def generate_test_cases(
    params: dict,
    logger,
    **kwargs
):
    """
    Generate final test cases using the user's GenTestCaseBussinessFlow class.
    Uses centralized LogEmitter for unified logging to console and WebSocket.

    Args:
        params: Dictionary containing all execution parameters
        logger: LogEmitter instance for centralized logging
        **kwargs: Additional parameters from the central executor

    Returns:
        Dict containing status, message, and output files
    """
    # Extract parameters from the standardized structure
    test_case_csv_path = kwargs.get("test_case_csv_path") or params.get("test_case_csv_path")
    output_dir = kwargs.get("output_dir") or params.get("output_dir")
    run_id = kwargs.get("run_id") or params.get("run_id")
    step_id = kwargs.get("step_id", "gen_test_case")

    # Extract processing parameters with defaults
    csv_conversion_summary_path = params.get("csv_conversion_summary_path")
    include_negative_tests = params.get("include_negative_tests", True)
    test_data_variants = params.get("test_data_variants", 3)

    try:
        pass

        # Setup paths using provided output_dir
        final_test_case_dir = os.path.join(output_dir, "final_test_cases")
        os.makedirs(final_test_case_dir, exist_ok=True)

        # Find required input files (CSV files from path_to_csv step)
        csv_input_dir = test_case_csv_path
        if not os.path.isdir(csv_input_dir):
            csv_input_dir = os.path.join(output_dir, "test_case_csv_generator")

        if not os.path.exists(csv_input_dir):
            await logger.log(run_id, "ERROR", f"❌ CSV input directory not found: {csv_input_dir}", step_id)
            raise FileNotFoundError(f"CSV input directory not found: {csv_input_dir}")

        await logger.log(run_id, "INFO", f"📁 Using CSV input directory: {csv_input_dir}", step_id)
        await logger.log(run_id, "INFO", f"📁 Output directory: {final_test_case_dir}", step_id)

        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))

        # Import and use the user's GenTestCaseBussinessFlow class directly

        try:
            # Try different import approaches
            try:
                from back_end.gen_test_case import GenTestCaseBussinessFlow
            except ImportError:
                # Fallback to direct import
                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "gen_test_case",
                    os.path.join(root_dir, "back_end", "gen_test_case.py")
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                GenTestCaseBussinessFlow = module.GenTestCaseBussinessFlow
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to import GenTestCaseBussinessFlow: {e}",
                "error_details": str(e)
            }

        # Process from the CSV directory structure

        try:
            # Use the process_from_base_dir method with CSV input
            result = GenTestCaseBussinessFlow.process_from_base_dir(
                base_dir=output_dir,
                output_dir=final_test_case_dir
            )
        except Exception as e:
            return {
                "status": "error",
                "message": f"Final test case generation failed: {e}",
                "error_details": str(e)
            }

        # Handle result (which is a list from process_from_base_dir)
        if isinstance(result, list):
            total_test_cases = sum(r.get("processed_files", 0) for r in result if isinstance(r, dict))
            test_files = []
            for r in result:
                if isinstance(r, dict) and "output_directory" in r:
                    test_files.append(r["output_directory"])
        else:
            total_test_cases = result.get("total_test_cases", 0) if result else 0
            test_files = result.get("test_files", []) if result else []

        # Create summary
        summary = {
            "total_test_cases": total_test_cases,
            "output_directory": final_test_case_dir,
            "include_negative_tests": include_negative_tests,
            "test_data_variants": test_data_variants,
            "generation_timestamp": None,
            "test_files": test_files,
            "business_flows_processed": len(result) if isinstance(result, list) else 1,
            "results": result if isinstance(result, list) else [result],
            "csv_input_directory": csv_input_dir
        }

        summary_path = os.path.join(output_dir, "test_cases_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        return {
            "status": "success",
            "message": f"Successfully generated final test cases for {len(result) if isinstance(result, list) else 1} business flows using user's GenTestCaseBussinessFlow class",
            "output_directory": final_test_case_dir,
            "summary_file": summary_path,
            "total_test_cases": total_test_cases,
            "business_flows_processed": len(result) if isinstance(result, list) else 1,
            "include_negative_tests": include_negative_tests,
            "test_data_variants": test_data_variants,
            "csv_input_directory": csv_input_dir
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Test case generation failed: {str(e)}",
            "error_details": str(e)
        }

@pipeline_step(
    name="Test Case Evaluator",
    description="Evaluates test cases using the user's TestCaseEvaluator class",
    outputs=["evaluation_results/", "quality_report.json", "benchmark_comparison.json"],
    inputs=["final_test_cases/", "test_cases_summary.json"],
    configurable_params={
        "evaluation_criteria": {
            "type": "select",
            "options": ["basic", "standard", "comprehensive"],
            "default": "standard",
            "description": "Level of evaluation criteria to apply"
        },
        "benchmark_type": {
            "type": "select",
            "options": ["industry_standard", "custom", "hybrid"],
            "default": "industry_standard",
            "description": "Type of benchmark to compare against"
        }
    }
)
async def evaluate_test_cases(
    params: dict,
    logger,
    **kwargs
):
    """Evaluate test cases using the user's TestCaseEvaluator class"""
    # Extract parameters from the standardized structure
    final_test_cases_path = kwargs.get("final_test_cases_path") or params.get("final_test_cases_path")
    test_cases_summary_path = kwargs.get("test_cases_summary_path") or params.get("test_cases_summary_path")
    output_dir = kwargs.get("output_dir") or params.get("output_dir")
    run_id = kwargs.get("run_id") or params.get("run_id")
    step_id = kwargs.get("step_id", "test_case_evaluator")

    # Extract processing parameters with defaults
    evaluation_criteria = params.get("evaluation_criteria", "standard")
    benchmark_type = params.get("benchmark_type", "industry_standard")

    try:
        # Setup paths
        evaluation_dir = os.path.join(output_dir, "evaluation_results")
        os.makedirs(evaluation_dir, exist_ok=True)

        # Find required input files (final test cases)
        test_case_dir = final_test_cases_path
        if not os.path.isdir(test_case_dir):
            test_case_dir = os.path.join(output_dir, "final_test_cases")

        if not os.path.exists(test_case_dir):
            raise FileNotFoundError(f"Final test case directory not found: {test_case_dir}")

        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))

        # Import and use the user's TestCaseEvaluator class directly

        try:
            # Try different import approaches
            try:
                from back_end.test_case_evaluator import TestCaseEvaluator
            except ImportError:
                # Fallback to direct import
                import importlib.util
                spec = importlib.util.spec_from_file_location(
                    "test_case_evaluator",
                    os.path.join(root_dir, "back_end", "test_case_evaluator.py")
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                TestCaseEvaluator = module.TestCaseEvaluator
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to import TestCaseEvaluator: {e}",
                "error_details": str(e)
            }

        # Setup required files for TestCaseEvaluator
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")
        benchmark_file = os.path.join(root_dir, "back_end", "document", "evaluation_benchmark.json")

        # Check if benchmark file exists, create a default one if not
        if not os.path.exists(benchmark_file):
            default_benchmark = {
                "evaluation_criteria": {
                    "completeness": {"weight": 0.3, "description": "Test case completeness"},
                    "clarity": {"weight": 0.2, "description": "Test case clarity"},
                    "coverage": {"weight": 0.3, "description": "Test coverage"},
                    "feasibility": {"weight": 0.2, "description": "Test feasibility"}
                },
                "quality_thresholds": {
                    "excellent": 0.9,
                    "good": 0.7,
                    "acceptable": 0.5,
                    "poor": 0.3
                }
            }
            with open(benchmark_file, 'w', encoding='utf-8') as f:
                json.dump(default_benchmark, f, indent=2, ensure_ascii=False)

        # Initialize the user's TestCaseEvaluator

        try:
            evaluator = TestCaseEvaluator(
                test_case_dir=test_case_dir,
                benchmark_file=benchmark_file,
                config_file=config_file,
                output_dir=evaluation_dir,
                rotate_api_key=False
            )
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to initialize TestCaseEvaluator: {e}",
                "error_details": str(e)
            }

        # Evaluate test cases

        try:
            # Use the run() method which returns a dict
            result = evaluator.run()
        except Exception as e:
            return {
                "status": "error",
                "message": f"Test case evaluation failed: {e}",
                "error_details": str(e)
            }

        # Create quality report
        quality_report_path = os.path.join(output_dir, "quality_report.json")
        benchmark_comparison_path = os.path.join(output_dir, "benchmark_comparison.json")

        quality_report = {
            "evaluation_summary": result.get("summary", {}),
            "criteria_used": evaluation_criteria,
            "benchmark_type": benchmark_type,
            "evaluation_timestamp": result.get("timestamp"),
            "total_test_cases_evaluated": result.get("total_evaluated", 0),
            "quality_score": result.get("quality_score", 0)
        }

        with open(quality_report_path, 'w', encoding='utf-8') as f:
            json.dump(quality_report, f, indent=2, ensure_ascii=False)

        # Create benchmark comparison
        benchmark_comparison = result.get("benchmark_comparison", {})
        with open(benchmark_comparison_path, 'w', encoding='utf-8') as f:
            json.dump(benchmark_comparison, f, indent=2, ensure_ascii=False)

        return {
            "status": "success",
            "message": "Test cases evaluated successfully using user's TestCaseEvaluator class",
            "output_directory": evaluation_dir,
            "quality_report_file": quality_report_path,
            "benchmark_comparison_file": benchmark_comparison_path,
            "total_evaluated": result.get("total_evaluated", 0),
            "quality_score": result.get("quality_score", 0),
            "evaluation_criteria": evaluation_criteria,
            "benchmark_type": benchmark_type
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Test case evaluation failed: {str(e)}",
            "error_details": str(e)
        }
