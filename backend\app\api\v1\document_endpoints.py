"""
Document processing endpoints with WebSocket integration
"""
import os
import asyncio
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import J<PERSON>NResponse
from typing import Optional, Dict, Any
from pydantic import BaseModel
import sys

# Add back_end to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, "../../../..")
back_end_path = os.path.join(project_root, "back_end")
if back_end_path not in sys.path:
    sys.path.insert(0, back_end_path)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from ...core.terminal_logger import get_terminal_logger
from ...core.websocket_manager import websocket_manager

router = APIRouter()

class DocumentProcessorOptions(BaseModel):
    skip_llm: bool = False
    use_auto_toc: bool = True
    enable_caching: bool = True
    overwrite_existing: bool = False

class DocumentProcessorResponse(BaseModel):
    status: str
    message: str
    output_dir: Optional[str] = None
    files_created: Optional[list] = None
    token_usage: Optional[Dict[str, Any]] = None

@router.post("/process", response_model=DocumentProcessorResponse)
async def process_document(
    file: UploadFile = File(...),
    skip_llm: bool = Form(False),
    use_auto_toc: bool = Form(True),
    enable_caching: bool = Form(True),
    overwrite_existing: bool = Form(False)
):
    """Process a PDF document with WebSocket status updates"""
    
    logger = get_terminal_logger("document_processor")
    
    try:
        # Validate file type
        if not file.filename.endswith('.pdf'):
            raise HTTPException(status_code=400, detail="Only PDF files are supported")
        
        # Create uploads directory if it doesn't exist
        uploads_dir = "uploads"
        os.makedirs(uploads_dir, exist_ok=True)
        
        # Save uploaded file
        file_path = os.path.join(uploads_dir, file.filename)
        
        logger.pipeline_update("document_processor", "starting")
        logger.info(f"Processing document: {file.filename}")
        
        # Check if file already exists and handle overwrite
        if os.path.exists(file_path) and not overwrite_existing:
            logger.warn(f"File {file.filename} already exists. Use overwrite_existing=true to replace.")
            return DocumentProcessorResponse(
                status="exists",
                message=f"File {file.filename} already exists. Set overwrite_existing=true to replace.",
                output_dir=None
            )
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.info(f"Saved file to: {file_path}")
        
        # Create output directory
        output_name = file.filename.replace('.pdf', '')
        output_dir = f"back_end/output/{output_name}"
        os.makedirs(output_dir, exist_ok=True)
        
        # Log processing options
        logger.info(f"Processing options: skip_llm={skip_llm}, use_auto_toc={use_auto_toc}, enable_caching={enable_caching}")
        
        # Update status
        logger.pipeline_update("document_processor", "running", 10)
        
        # Import and run document processor
        try:
            from back_end.document_processor import DocumentProcessor
            
            processor = DocumentProcessor(
                pdf_path=file_path,
                output_dir=f"{output_dir}/document_processor",
                merged_json=f"{output_dir}/document_processor/merged_output.json",
                analyzed_json=f"{output_dir}/document_processor/description_output.json",
                use_auto_toc=use_auto_toc,
                count_token=True,
                enable_caching=enable_caching
            )
            
            logger.pipeline_update("document_processor", "running", 30)
            logger.info("Extracting PDF content...")
            
            # Process PDF
            processor.process_pdf()
            logger.pipeline_update("document_processor", "running", 60)
            logger.info("Creating merged JSON...")
            
            # Create merged JSON
            processor.merge_to_json()
            logger.pipeline_update("document_processor", "running", 80)
            
            # Analyze with LLM if not skipped
            if not skip_llm:
                logger.info("Analyzing content with LLM...")
                processor.analyze_json()
            else:
                logger.info("Skipping LLM analysis as requested")
            
            logger.pipeline_update("document_processor", "running", 90)
            
            # Get token usage
            token_summary = processor.get_token_summary() if processor.count_token else None
            
            # List created files
            files_created = []
            doc_processor_dir = f"{output_dir}/document_processor"
            if os.path.exists(doc_processor_dir):
                files_created = [f for f in os.listdir(doc_processor_dir) if f.endswith('.json')]
            
            logger.pipeline_update("document_processor", "completed", 100)
            logger.info(f"✅ Document processing completed successfully!")
            logger.info(f"Output directory: {output_dir}")
            logger.info(f"Files created: {', '.join(files_created)}")
            
            if token_summary:
                logger.info(f"Token usage: {token_summary.get('total_cost_usd', 0):.4f} USD")
            
            return DocumentProcessorResponse(
                status="completed",
                message="Document processed successfully",
                output_dir=output_dir,
                files_created=files_created,
                token_usage=token_summary
            )
            
        except ImportError as e:
            logger.error(f"Failed to import DocumentProcessor: {e}")
            logger.pipeline_update("document_processor", "failed")
            raise HTTPException(status_code=500, detail=f"Module import error: {e}")
            
    except Exception as e:
        logger.error(f"Document processing failed: {str(e)}")
        logger.pipeline_update("document_processor", "failed")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@router.get("/check-existing/{filename}")
async def check_existing_output(filename: str):
    """Check if output already exists for a given filename"""
    output_name = filename.replace('.pdf', '')
    output_dir = f"back_end/output/{output_name}"
    
    exists = os.path.exists(output_dir)
    files = []
    
    if exists:
        doc_processor_dir = f"{output_dir}/document_processor"
        if os.path.exists(doc_processor_dir):
            files = [f for f in os.listdir(doc_processor_dir) if f.endswith('.json')]
    
    return {
        "exists": exists,
        "output_dir": output_dir if exists else None,
        "files": files
    } 