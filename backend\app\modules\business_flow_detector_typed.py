"""
Type-hinted Business Flow Detector Module
Provides strict typing and contracts for business flow generation functionality.
"""
import os
import sys
import logging
from typing import Dict, Any, Optional
from pathlib import Path

from ..contracts.api_contracts import BusinessFlowParameters, PipelineStepResult
from ..core.log_emitter import LogEmitter

# Get the root directory for imports
root_dir = Path(__file__).parent.parent.parent.parent

async def execute(
    params: BusinessFlowParameters,
    logger: LogEmitter,
    run_id: str,
    input_path: str,
    output_dir: str,
    **kwargs
) -> PipelineStepResult:
    """
    Generate business flows using the user's GenBusinessFlow class.
    
    Args:
        input_path: Path to input JSON file (merged output from document processor)
        output_dir: Dynamic output directory for this session
        logger: LogEmitter instance for centralized logging
        run_id: Pipeline run identifier
        max_flows: Maximum number of business flows to generate (1-50)
        count_token: Whether to count tokens and calculate costs
        **kwargs: Additional parameters
        
    Returns:
        PipelineStepResult: Structured result with status, message, and output files
        
    Raises:
        FileNotFoundError: If required input files are not found
        ImportError: If GenBusinessFlow class cannot be imported
        ValueError: If parameters are invalid
    """
    step_id = kwargs.get("step_id", "business_flow_detector")

    try:
        # Parameters are already validated as Pydantic model
        
        await logger.log(run_id, "INFO", "🚀 Starting Business Flow Generation...", step_id)
        
        # Log configuration as a block
        config_info = f"""📋 Business Flow Configuration:
   • Input file: {os.path.basename(input_path)}
   • Output directory: {output_dir}
   • Max flows: {params.max_flows}
   • Count tokens: {params.count_token}"""
        await logger.log_block(run_id, "Configuration", config_info, step_id)
        
        # Setup paths using provided output_dir
        business_flows_dir = os.path.join(output_dir, "business_flows")
        os.makedirs(business_flows_dir, exist_ok=True)
        
        await logger.log(run_id, "INFO", f"📁 Created business flows directory: {business_flows_dir}", step_id)
        
        # Find the merged JSON file from document processor
        merged_json = os.path.join(output_dir, "document_processor", "merged_output.json")
        if not os.path.exists(merged_json):
            # Try alternative paths
            alt_paths = [
                input_path,
                os.path.join(output_dir, "merged_output.json"),
                os.path.join(os.path.dirname(input_path), "merged_output.json")
            ]
            
            for alt_path in alt_paths:
                if os.path.exists(alt_path):
                    merged_json = alt_path
                    break
            else:
                await logger.log(run_id, "ERROR", f"❌ Required input file not found: {merged_json}", step_id)
                raise FileNotFoundError(f"Required input file not found: {merged_json}")
        
        await logger.log(run_id, "INFO", f"📄 Using input file: {merged_json}", step_id)
        
        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))
        
        # Import and use the user's GenBusinessFlow class directly
        await logger.log(run_id, "INFO", "📦 Importing GenBusinessFlow class...", step_id)
        
        try:
            from back_end.BusinessFlowDetector import GenBusinessFlow
            await logger.log(run_id, "SUCCESS", "✅ Successfully imported GenBusinessFlow", step_id)
        except ImportError as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to import GenBusinessFlow: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Failed to import GenBusinessFlow: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Setup file paths
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")
        
        # Initialize the user's GenBusinessFlow
        await logger.log(run_id, "INFO", "🔧 Initializing GenBusinessFlow...", step_id)
        
        try:
            detector = GenBusinessFlow(
                input_json_file=merged_json,
                business_flow_dir=business_flows_dir,
                config_file=config_file,
                count_token=params.count_token
            )
            await logger.log(run_id, "SUCCESS", "✅ GenBusinessFlow initialized successfully", step_id)
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to initialize GenBusinessFlow: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Failed to initialize GenBusinessFlow: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Execute business flow generation
        await logger.log(run_id, "INFO", "🚀 Starting business flow generation...", step_id)
        await logger.log(run_id, "INFO", f"   Generating up to {params.max_flows} business flows...", step_id)
        
        try:
            # Call the execute method
            result = detector.execute(max_flows=params.max_flows)
            
            if result and result.get("status") == "success":
                await logger.log(run_id, "SUCCESS", "✅ Business flow generation completed successfully", step_id)
                
                # Log output files
                output_files = []
                if os.path.exists(business_flows_dir):
                    output_files = [os.path.join(business_flows_dir, f) for f in os.listdir(business_flows_dir)]
                    if output_files:
                        files_info = "\n".join([f"   • {os.path.basename(f)}" for f in output_files])
                        await logger.log_block(run_id, "Generated Files", f"📄 Output Files:\n{files_info}", step_id)
                
                return PipelineStepResult(
                    status="success",
                    message="Business flow generation completed successfully",
                    step_id=step_id,
                    output_files=output_files,
                    metadata={"max_flows": params.max_flows, "count_token": params.count_token}
                )
            else:
                error_msg = result.get("message", "Unknown error") if result else "No result returned"
                await logger.log(run_id, "ERROR", f"❌ Business flow generation failed: {error_msg}", step_id)
                return PipelineStepResult(
                    status="error",
                    message=f"Business flow generation failed: {error_msg}",
                    step_id=step_id,
                    error_details=str(result) if result else "No result returned"
                )
                
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Exception during business flow generation: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Exception during business flow generation: {e}",
                step_id=step_id,
                error_details=str(e)
            )
            
    except Exception as e:
        await logger.log(run_id, "ERROR", f"❌ Unexpected error in business flow generation: {e}", step_id)
        return PipelineStepResult(
            status="error",
            message=f"Unexpected error: {e}",
            step_id=step_id,
            error_details=str(e)
        )
