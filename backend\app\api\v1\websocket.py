"""
WebSocket endpoints for real-time terminal communication
"""
import asyncio
import json
import logging
from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from ...core.websocket_manager import websocket_manager
from ...core.log_emitter import LogEmitter

router = APIRouter()

# Global set to track live terminal connections
live_terminal_connections = set()

@router.websocket("/terminal")
async def websocket_terminal(websocket: WebSocket):
    """WebSocket endpoint for terminal communication"""
    await websocket_manager.connect(websocket)
    
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            
            # Echo back for now (you can add command handling here later)
            if data.strip():
                await websocket_manager.send_log(
                    f"Received: {data}",
                    level="info", 
                    module="terminal"
                )
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)
    except Exception as e:
        websocket_manager.disconnect(websocket)
        print(f"WebSocket error: {e}")

@router.websocket("/terminal/live")
async def websocket_live_terminal(websocket: WebSocket):
    """WebSocket endpoint for live terminal logs - shows real-time backend activity"""
    await websocket.accept()

    # Check if this is a reconnection (avoid duplicate welcome messages)
    is_reconnection = len(live_terminal_connections) > 0
    live_terminal_connections.add(websocket)

    try:
        # Only send welcome messages for new connections, not reconnections
        if not is_reconnection:
            # Send welcome message
            await websocket.send_text(json.dumps({
                "type": "log",
                "level": "info",
                "message": "🟢 Auto-connected to live backend terminal",
                "step_id": "system",
                "timestamp": datetime.now().isoformat()
            }))

            # Send initial status
            await websocket.send_text(json.dumps({
                "type": "log",
                "level": "info",
                "message": "📡 Live terminal ready - monitoring all backend activity",
                "step_id": "system",
                "timestamp": datetime.now().isoformat()
            }))
        else:
            # Just send a quiet reconnection message
            await websocket.send_text(json.dumps({
                "type": "log",
                "level": "debug",
                "message": "🔄 Reconnected to live terminal",
                "step_id": "system",
                "timestamp": datetime.now().isoformat()
            }))

        # Send a heartbeat message every 30 seconds
        import asyncio
        async def send_heartbeat():
            while True:
                try:
                    await asyncio.sleep(60)
                    await websocket.send_text(json.dumps({
                        "type": "log",
                        "level": "debug",
                        "message": "💓 Live terminal heartbeat",
                        "step_id": "system",
                        "timestamp": datetime.now().isoformat()
                    }))
                except:
                    break

        # Start heartbeat task
        heartbeat_task = asyncio.create_task(send_heartbeat())

        # Keep connection alive and handle incoming messages
        while True:
            try:
                data = await websocket.receive_text()

                # Handle ping/pong for connection health
                if data.strip() == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }))

            except WebSocketDisconnect:
                break
            except Exception as e:
                logging.error(f"Live terminal WebSocket error: {e}")
                break

    except Exception as e:
        logging.error(f"Live terminal connection error: {e}")
    finally:
        # Cancel heartbeat task
        if 'heartbeat_task' in locals():
            heartbeat_task.cancel()
        live_terminal_connections.discard(websocket)

# Function to broadcast logs to all live terminal connections
async def broadcast_to_live_terminals(log_data: dict):
    """Broadcast log message to all connected live terminals"""
    print(f"[DEBUG] broadcast_to_live_terminals called with: {log_data}")
    print(f"[DEBUG] live_terminal_connections count: {len(live_terminal_connections)}")

    if not live_terminal_connections:
        print("[DEBUG] No live terminal connections, returning")
        return

    # Prepare message
    message = json.dumps({
        "type": "log",
        "timestamp": datetime.now().isoformat(),
        **log_data
    })
    print(f"[DEBUG] Sending message to {len(live_terminal_connections)} connections: {message[:100]}...")

    # Send to all connected terminals
    disconnected = set()
    for websocket in live_terminal_connections:
        try:
            await websocket.send_text(message)
            print(f"[DEBUG] Successfully sent message to websocket")
        except Exception as e:
            print(f"[DEBUG] Failed to send message to websocket: {e}")
            disconnected.add(websocket)

    # Clean up disconnected websockets
    for websocket in disconnected:
        live_terminal_connections.discard(websocket)
        print(f"[DEBUG] Removed disconnected websocket")
