"""
Type-hinted Path Finder Module
Provides strict typing and contracts for screen path generation functionality.
"""
import os
import sys
import logging
import importlib.util
from typing import Dict, Any, Optional
from pathlib import Path

from ..contracts.api_contracts import PathFinderParameters, PipelineStepResult
from ..core.log_emitter import LogEmitter

# Get the root directory for imports
root_dir = Path(__file__).parent.parent.parent.parent

async def execute(
    params: PathFinderParameters,
    logger: LogEmitter,
    run_id: str,
    relevant_content_path: str,
    output_dir: str,
    **kwargs
) -> PipelineStepResult:
    """
    Generate screen paths using the user's ScreenPathGenerator class.
    
    Args:
        relevant_content_path: Path to relevant content directory or file
        output_dir: Dynamic output directory for this session
        logger: LogEmitter instance for centralized logging
        run_id: Pipeline run identifier
        screen_graph_path: Optional path to screen graph file
        max_path_depth: Maximum depth for path generation (1-20)
        count_token: Whether to count tokens and calculate costs
        **kwargs: Additional parameters
        
    Returns:
        PipelineStepResult: Structured result with status, message, and output files
        
    Raises:
        FileNotFoundError: If required input files are not found
        ImportError: If ScreenPathGenerator class cannot be imported
        ValueError: If parameters are invalid
    """
    step_id = kwargs.get("step_id", "path_finder")

    try:
        # Parameters are already validated as Pydantic model
        
        await logger.log(run_id, "INFO", "🚀 Starting Screen Path Generation...", step_id)
        
        # Log configuration as a block
        config_info = f"""📋 Path Finder Configuration:
   • Relevant content path: {relevant_content_path}
   • Output directory: {output_dir}
   • Max path depth: {params.max_path_depth}
   • Count tokens: {params.count_token}"""
        await logger.log_block(run_id, "Configuration", config_info, step_id)
        
        # Setup paths using provided output_dir
        path_processor_dir = os.path.join(output_dir, "path_processor")
        os.makedirs(path_processor_dir, exist_ok=True)
        
        await logger.log(run_id, "INFO", f"📁 Created path processor directory: {path_processor_dir}", step_id)
        
        # Find required input files
        # 1. Relevant content directory
        relevant_content_dir = relevant_content_path
        if not os.path.isdir(relevant_content_dir):
            relevant_content_dir = os.path.join(output_dir, "relevant_content_processor")
        
        if not os.path.exists(relevant_content_dir):
            await logger.log(run_id, "ERROR", f"❌ Relevant content directory not found: {relevant_content_dir}", step_id)
            raise FileNotFoundError(f"Relevant content directory not found: {relevant_content_dir}")
        
        await logger.log(run_id, "INFO", f"📁 Using relevant content directory: {relevant_content_dir}", step_id)
        
        # 2. Business flows directory (for context)
        business_flows_dir = os.path.join(output_dir, "business_flows")
        if not os.path.exists(business_flows_dir):
            await logger.log(run_id, "WARNING", f"⚠️ Business flows directory not found: {business_flows_dir}", step_id)
            await logger.log(run_id, "INFO", "   Proceeding without business flows context...", step_id)
        else:
            await logger.log(run_id, "INFO", f"📁 Using business flows directory: {business_flows_dir}", step_id)
        
        # Set UTF-8 encoding environment
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # Add the root directory to Python path for imports
        if str(root_dir) not in sys.path:
            sys.path.insert(0, str(root_dir))
        
        # Import and use the user's ScreenPathGenerator class
        await logger.log(run_id, "INFO", "📦 Importing ScreenPathGenerator class...", step_id)
        
        try:
            # Try different import approaches
            try:
                from back_end.path_processor import ScreenPathGenerator
                await logger.log(run_id, "SUCCESS", "✅ Successfully imported ScreenPathGenerator (direct import)", step_id)
            except ImportError:
                # Fallback to dynamic import
                await logger.log(run_id, "INFO", "   Trying dynamic import...", step_id)
                spec = importlib.util.spec_from_file_location(
                    "path_processor",
                    os.path.join(root_dir, "back_end", "path_processor.py")
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                ScreenPathGenerator = module.ScreenPathGenerator
                await logger.log(run_id, "SUCCESS", "✅ Successfully imported ScreenPathGenerator (dynamic import)", step_id)
                
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Failed to import ScreenPathGenerator: {e}", step_id)
            return PipelineStepResult(
                status="error",
                message=f"Failed to import ScreenPathGenerator: {e}",
                step_id=step_id,
                error_details=str(e)
            )
        
        # Setup configuration file path
        config_file = os.path.join(root_dir, "back_end", "document", "gemini_config_flash.json")
        
        # Process each business flow (following the real implementation pattern)
        business_flow_files = []
        if os.path.exists(business_flows_dir):
            business_flow_files = [f for f in os.listdir(business_flows_dir)
                                 if f.startswith("Business Flow") and f.endswith(".txt")]

        if not business_flow_files:
            await logger.log(run_id, "WARNING", "⚠️ No business flow files found, using default flow", step_id)
            business_flow_files = ["default_flow"]

        await logger.log(run_id, "INFO", f"🚀 Processing {len(business_flow_files)} business flows...", step_id)

        results = []
        for i, flow_file in enumerate(business_flow_files, 1):
            try:
                await logger.log(run_id, "INFO", f"Processing business flow {i}: {flow_file}", step_id)

                # Initialize the user's ScreenPathGenerator for each business flow
                flows_output_path = os.path.join(path_processor_dir, f"business_flow_{i}")
                screen_paths_output_path = os.path.join(path_processor_dir, f"screen_paths_{i}")

                generator = ScreenPathGenerator(
                    config_file=config_file,
                    relevant_content_base_dir=relevant_content_dir,
                    business_flows_dir=business_flows_dir,
                    business_flow_number=i,
                    flows_output_path=flows_output_path,
                    screen_paths_output_path=screen_paths_output_path,
                    base_output_dir=output_dir,
                    count_token=params.count_token
                )

                await logger.log(run_id, "SUCCESS", f"✅ ScreenPathGenerator initialized for flow {i}", step_id)

                # Generate screen paths using execute method
                await logger.log(run_id, "INFO", f"Generating screen paths for business flow {i}...", step_id)
                result = generator.execute()
                results.append({
                    "business_flow": i,
                    "result": result,
                    "flows_output": flows_output_path,
                    "screen_paths_output": screen_paths_output_path
                })
                await logger.log(run_id, "SUCCESS", f"✅ Successfully processed business flow {i}", step_id)

            except Exception as e:
                await logger.log(run_id, "ERROR", f"❌ Error processing business flow {i}: {e}", step_id)
                results.append({
                    "business_flow": i,
                    "error": str(e)
                })
        
        # Generate summary and return results
        processed_flows = len([r for r in results if "error" not in r])
        failed_flows = len([r for r in results if "error" in r])

        await logger.log(run_id, "INFO", f"📊 Processing Summary:", step_id)
        await logger.log(run_id, "INFO", f"   • Total flows: {len(business_flow_files)}", step_id)
        await logger.log(run_id, "INFO", f"   • Processed: {processed_flows}", step_id)
        await logger.log(run_id, "INFO", f"   • Failed: {failed_flows}", step_id)

        # Log output files
        output_files = []
        if os.path.exists(path_processor_dir):
            output_files = [os.path.join(path_processor_dir, f) for f in os.listdir(path_processor_dir)]
            if output_files:
                files_info = "\n".join([f"   • {os.path.basename(f)}" for f in output_files])
                await logger.log_block(run_id, "Generated Files", f"📄 Output Files:\n{files_info}", step_id)

        if processed_flows > 0:
            await logger.log(run_id, "SUCCESS", "✅ Screen path generation completed successfully", step_id)
            return PipelineStepResult(
                status="success",
                message="Screen path generation completed successfully",
                step_id=step_id,
                output_files=output_files,
                metadata={
                    "max_path_depth": params.max_path_depth,
                    "count_token": params.count_token,
                    "total_flows": len(business_flow_files),
                    "processed_flows": processed_flows,
                    "failed_flows": failed_flows
                }
            )
        else:
            await logger.log(run_id, "ERROR", "❌ All business flows failed to process", step_id)
            return PipelineStepResult(
                status="error",
                message="All business flows failed to process",
                step_id=step_id,
                error_details=str(results)
            )
            
    except Exception as e:
        await logger.log(run_id, "ERROR", f"❌ Unexpected error in screen path generation: {e}", step_id)
        return PipelineStepResult(
            status="error",
            message=f"Unexpected error: {e}",
            step_id=step_id,
            error_details=str(e)
        )
