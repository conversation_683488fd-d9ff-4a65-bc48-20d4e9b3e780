"""
LogEmitter: A testable logging interface with strict type contracts.
This class provides a clean interface for emitting structured logs during pipeline execution.
"""
from typing import Dict, Any, Optional, Protocol
from datetime import datetime
import json

from ..models.contracts import LogLevel, LogMessage

class ConnectionManagerProtocol(Protocol):
    """Protocol for connection manager to make LogEmitter testable"""
    async def broadcast_log(self, log_data: Dict[str, Any]) -> None:
        """Broadcast log message to connected clients"""
        ...

class LogEmitter:
    """
    Structured logging emitter with strict type contracts.
    
    This class ensures all logs follow a consistent format and provides
    a clean interface that can be easily mocked for testing.
    """
    
    def __init__(
        self, 
        connection_manager: ConnectionManagerProtocol,
        module_name: str,
        run_id: Optional[str] = None
    ) -> None:
        """
        Initialize LogEmitter with required dependencies.
        
        Args:
            connection_manager: Connection manager for broadcasting logs
            module_name: Name of the module emitting logs
            run_id: Optional run ID for tracking execution context
        """
        self.connection_manager = connection_manager
        self.module_name = module_name
        self.run_id = run_id
    
    async def log(
        self,
        run_id: str,
        level: str,
        message: str,
        step_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> LogMessage:
        """
        PHOENIX PROJECT: Emit a structured log message.

        Args:
            run_id: The run ID for this execution
            level: Log level (INFO, ERROR, SUCCESS, etc.)
            message: The log message text
            step_id: Optional step ID for pipeline context
            metadata: Optional additional metadata (tokens, cost, etc.)

        Returns:
            LogMessage: The structured log message that was emitted
        """
        # Convert string level to LogLevel enum
        log_level = getattr(LogLevel, level.upper(), LogLevel.INFO)

        log_msg = LogMessage(
            timestamp=datetime.now().isoformat(),
            level=log_level,
            module=self.module_name,
            message=message,
            run_id=run_id,
            step_id=step_id,
            metadata=metadata or {}
        )

        # Convert to dict for broadcasting
        log_data = log_msg.model_dump()

        # Broadcast to connection manager for pipeline runs (not system logs)
        if run_id != "system":
            try:
                await self.connection_manager.broadcast_log(run_id, log_data)
            except Exception:
                # Fail silently if connection manager is not available
                pass

        # Note: Live terminal broadcasting is handled by WebSocketLogHandler
        # to avoid duplication. LogEmitter only handles run_id specific connections.

        return log_msg
    
    async def log_block(
        self,
        run_id: str,
        title: str,
        content: Dict[str, Any],
        level: str = "INFO",
        step_id: Optional[str] = None
    ) -> LogMessage:
        """
        PHOENIX PROJECT: Emit a structured log block with title and content.

        Args:
            run_id: The run ID for this execution
            title: Block title
            content: Structured content as dictionary
            level: Log level
            step_id: Optional step ID for pipeline context

        Returns:
            LogMessage: The structured log message that was emitted
        """
        metadata = {
            "block_type": "structured",
            "content": content
        }

        return await self.log(
            run_id=run_id,
            level=level,
            message=title,
            step_id=step_id,
            metadata=metadata
        )
    
    async def debug(self, run_id: str, message: str, step_id: Optional[str] = None, **kwargs) -> LogMessage:
        """Emit debug level log"""
        return await self.log(run_id, "DEBUG", message, step_id, **kwargs)

    async def info(self, run_id: str, message: str, step_id: Optional[str] = None, **kwargs) -> LogMessage:
        """Emit info level log"""
        return await self.log(run_id, "INFO", message, step_id, **kwargs)

    async def warning(self, run_id: str, message: str, step_id: Optional[str] = None, **kwargs) -> LogMessage:
        """Emit warning level log"""
        return await self.log(run_id, "WARNING", message, step_id, **kwargs)

    async def error(self, run_id: str, message: str, step_id: Optional[str] = None, **kwargs) -> LogMessage:
        """Emit error level log"""
        return await self.log(run_id, "ERROR", message, step_id, **kwargs)

    async def critical(self, run_id: str, message: str, step_id: Optional[str] = None, **kwargs) -> LogMessage:
        """Emit critical level log"""
        return await self.log(run_id, "CRITICAL", message, step_id, **kwargs)


# Singleton Logger Factory Pattern
# This ensures the entire application uses the same logger instance

class MockConnectionManager:
    """Mock connection manager for when no real connection manager is available"""
    async def broadcast_log(self, run_id: str, log_data: Dict[str, Any]) -> None:
        """Mock broadcast - does nothing"""
        pass

# Create a single, application-wide instance
_log_emitter_instance: Optional[LogEmitter] = None

def get_logger() -> LogEmitter:
    """
    Returns the singleton instance of the LogEmitter.

    This factory function ensures that the entire application uses the exact same
    logger instance, which is crucial for stateful components like our ConnectionManager.

    Benefits:
    - Consistent state across the application
    - Resource efficiency (single instance)
    - Centralized configuration
    - Easy to mock for testing

    Returns:
        LogEmitter: The singleton logger instance
    """
    global _log_emitter_instance

    if _log_emitter_instance is None:
        # Try to get the real connection manager, fall back to mock
        try:
            from .websocket_manager import connection_manager
            conn_mgr = connection_manager
        except ImportError:
            # Use mock connection manager if real one isn't available
            conn_mgr = MockConnectionManager()

        _log_emitter_instance = LogEmitter(
            connection_manager=conn_mgr,
            module_name="pipeline_executor",
            run_id=None  # Will be set per execution
        )

    return _log_emitter_instance

def set_logger_run_id(run_id: str) -> None:
    """
    Set the run_id for the singleton logger instance.

    Args:
        run_id: The run ID to set for tracking execution context
    """
    logger = get_logger()
    logger.run_id = run_id
