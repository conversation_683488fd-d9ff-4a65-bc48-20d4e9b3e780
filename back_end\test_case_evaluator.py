import os
import json
import glob
import asyncio
from typing import Dict, List, Any, Optional, Set
import logging
import datetime
import argparse
import pandas as pd
import google.generativeai as genai
import ftfy  # type: ignore # Thêm thư viện ftfy để sửa lỗi JSON
import re  # Thêm thư viện re để phân tích tiêu chí boundary
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
try:
    from .rotate_api_key import APIKeyRotator
except ImportError:
    from rotate_api_key import APIKeyRotator
from google.api_core.exceptions import ResourceExhausted

# Đảm bảo file prompt_storing.py của bạn chứa prompt này
from prompt_storing import COVERAGE_ANALYSIS_PROMPT

def setup_logging():
    """Set up logging configuration - WebSocket handler will capture these logs with delay control."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("full_suite_analysis.log", mode='w')
        ]
    )

def parse_boundary_criterion(description: str) -> tuple[list[str], list[str]]:
    """Parse boundary criterion description to extract fields and conditions."""
    fields_match = re.search(r'for\s+([^\(]+)\s*\(([^)]+)\)', description)
    if fields_match:
        fields = [f.strip() for f in fields_match.group(1).split(',')]
        conditions = [c.strip() for c in fields_match.group(2).split(',')]
        # Nếu có "etc.", thêm các điều kiện boundary mặc định
        if 'etc' in conditions or 'etc.' in conditions:
            default_conditions = ['empty', '1 char', '255 chars', '256 chars']
            conditions = [c for c in conditions if c not in ['etc', 'etc.']] + default_conditions
        return fields, list(set(conditions))  # Loại bỏ trùng lặp
    return [], []

class FileParser:
    """A helper class to parse different test case file formats."""
    @staticmethod
    def parse(file_path: str) -> List[Dict]:
        if file_path.endswith('.csv'):
            return FileParser._parse_csv(file_path)
        elif file_path.endswith('.json'):
            return FileParser._parse_json(file_path)
        else:
            logging.warning(f"Unsupported file type for parsing: {file_path}. Skipping.")
            return []

    @staticmethod
    def _parse_csv(file_path: str) -> List[Dict]:
        try:
            df = pd.read_csv(file_path, keep_default_na=False).dropna(how='all')
            df.columns = df.columns.str.strip()  # Loại bỏ khoảng trắng ở header
            test_cases = df.to_dict('records')
            logging.info(f"Successfully parsed {len(test_cases)} test cases from CSV: {os.path.basename(file_path)}")
            return test_cases
        except Exception as e:
            logging.error(f"Error parsing CSV file {file_path}: {e}")
            return []

    @staticmethod
    def _parse_json(file_path: str) -> List[Dict]:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = json.load(f)
            if isinstance(content, list):
                return content
            elif isinstance(content, dict):
                return [content]
            return []
        except Exception as e:
            logging.error(f"Error parsing JSON file {file_path}: {e}")
            return []

class CoverageAnalyzer:
    """Worker class: Analyzes a SINGLE test suite for a specific feature."""
    def __init__(self, feature_name: str, test_case_files: List[str], benchmark_file: str, config: Dict, output_dir: str, config_path: str):
        self.feature_name = feature_name
        self.test_case_files = test_case_files
        self.benchmark_file = benchmark_file
        self.config = config
        self.config_path = config_path
        self.output_dir = output_dir
        self.rotator = None  # Initialize first
        self.model = self._configure_gemini()
        self.benchmark_data = self._load_json(self.benchmark_file)

        os.makedirs(self.output_dir, exist_ok=True)
    
    def _load_json(self, file_path: str) -> Optional[Dict]:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"[{self.feature_name}] Failed to load JSON from {file_path}: {e}")
            return None

    def _configure_gemini(self) -> Optional[genai.GenerativeModel]:
        try:
            # Initialize APIKeyRotator
            self.rotator = APIKeyRotator(config_path=self.config_path)
            if not self.rotator.api_keys:
                logging.error(f"[{self.feature_name}] APIKeyRotator failed to load any keys")
                self.rotator = None  # Ensure it's None on failure
                return None
            
            api_key = self.rotator.get_api_key()
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel(
                self.config.get("config_gemini_model", {}).get("model_name", "gemini-2.5-flash"),
                generation_config={"response_mime_type": "application/json"}
            )
            logging.info(f"[{self.feature_name}] Configured with API key ending: ...{api_key[-4:]}")
            return model
        except Exception as e:
            logging.error(f"[{self.feature_name}] Error configuring Gemini: {e}")
            self.rotator = None  # Ensure it's None on failure
            return None

    def _rotate_and_reconfigure_api(self) -> bool:
        """Rotate API key and reconfigure model"""
        try:
            current_key = self.rotator.get_api_key()
            logging.info(f"[{self.feature_name}] Rotating from key ending: ...{current_key[-4:]}")
            
            self.rotator.rotate_api_key()
            new_key = self.rotator.get_api_key()
            genai.configure(api_key=new_key)
            self.model = genai.GenerativeModel(
                self.config.get("config_gemini_model", {}).get("model_name", "gemini-2.5-flash"),
                generation_config={"response_mime_type": "application/json"}
            )
            logging.info(f"[{self.feature_name}] Switched to key ending: ...{new_key[-4:]}")
            return True
        except Exception as e:
            logging.error(f"[{self.feature_name}] Error during API rotation: {e}")
            return False

    def _evaluate_with_retry(self, prompt: str, max_attempts: int = None) -> str:
        """Evaluate with API key rotation on quota exhaustion"""
        if not self.rotator:
            return "Error: API rotator not initialized"
            
        max_attempts = max_attempts or len(self.rotator.api_keys)
        
        for attempt in range(max_attempts):
            try:
                current_key = self.rotator.get_api_key()
                logging.info(f"[{self.feature_name}] Attempt {attempt + 1}/{max_attempts} with key: ...{current_key[-4:]}")
                
                response = self.model.generate_content(prompt)
                if response and response.text:
                    return response.text
                else:
                    return "Error: No response text generated"
                    
            except ResourceExhausted as e:
                logging.warning(f"[{self.feature_name}] Quota exceeded on attempt {attempt + 1}")
                if attempt < max_attempts - 1:
                    if not self._rotate_and_reconfigure_api():
                        return f"Error: Failed to rotate API key - {e}"
                else:
                    logging.error(f"[{self.feature_name}] Exhausted all {max_attempts} API keys")
                    return f"Error: All API keys exhausted - {e}"
            except Exception as e:
                logging.error(f"[{self.feature_name}] Unexpected error on attempt {attempt + 1}: {e}")
                return f"Error: {e}"
                
        return "Error: Evaluation failed after all attempts"

    def _load_all_sub_test_cases(self) -> List[Dict]:
        """Parses all test case files for this feature into a single list."""
        all_test_cases = []
        for file_path in self.test_case_files:
            parsed_cases = FileParser.parse(file_path)
            for case in parsed_cases:
                cleaned_case = {}
                for key, value in case.items():
                    if isinstance(value, str):
                        # Thoát ký tự đặc biệt và loại bỏ ký tự điều khiển
                        value = value.replace('\n', '\\n').replace('\r', '\\r').replace('"', '\\"').replace('\0', '')
                    cleaned_case[key] = value
                all_test_cases.append(cleaned_case)
        
        logging.info(f"[{self.feature_name}] Loaded a total of {len(all_test_cases)} sub-test cases from {len(self.test_case_files)} file(s).")
        return all_test_cases

    async def run_analysis(self) -> Optional[Dict]:
        if not self.model or not self.benchmark_data:
            logging.error(f"[{self.feature_name}] Analyzer is not properly configured. Aborting.")
            return None

        all_sub_test_cases = self._load_all_sub_test_cases()
        if not all_sub_test_cases:
            logging.warning(f"[{self.feature_name}] No test cases found. Aborting analysis for this feature.")
            return None

        # Lọc section liên quan đến feature hiện tại
        feature_to_section_map = {
            # "Testcase_(UC-101)": "UC-101_USER_REGISTRATION",
            # "Testcase_(UC-102)": "UC-102_USER_LOGIN",
            # "Testcase_(Product_Browsing)": "UC_PRODUCT_BROWSING",
            # "Testcase_(UC-203)": "UC-203_ADD_TO_CART",
            # "Testcase_(UC-204_205)": "UC-204_205_SHOPPING_CART",
            # "Testcase_(UC-207)": "UC-207_CHECKOUT",
            # "Testcase_(UC_Order_Management)": "UC_ORDER_MANAGEMENT",
            # "Testcase_(UC-212)": "UC-212_CANCEL_ORDER", 
            # "Testcase_(UC_Profile_Management)": "UC_PROFILE_MANAGEMENT",  
            # "Testcase_(UC-302_405)": "UC-302_405_PRODUCT_MANAGEMENT",  
            # "Testcase_(UC-310_409)": "UC-310_409_DASHBOARD",  
            # "Testcase_(UC-404)": "UC-404_MANAGE_CATEGORIES",
            # "Testcase_(UC-211)": "UC-211_VIEW_BOOKING_HISTORY",
            # "Testcase_(Manage_Addresses)": "UC-MANAGE_ADDRESSES",
            # "Testcase_(Manage_Users)": "UC-MANAGE_USERS"
        }

        section_id = feature_to_section_map.get(self.feature_name, None)
        
        filtered_benchmark = self.benchmark_data
        if section_id:
            filtered_benchmark_sections = [
                section for section in self.benchmark_data["evaluationSections"]
                if section["sectionId"] == section_id
            ]
            if filtered_benchmark_sections:
                 filtered_benchmark = {"evaluationSections": filtered_benchmark_sections}
            else:
                logging.warning(f"[{self.feature_name}] No relevant benchmark section found for {section_id}. Using full benchmark.")

        # ==================== PHẦN SỬA ĐỔI QUAN TRỌNG ====================
        # Tạo một bản sao sâu để làm sạch mà không ảnh hưởng đến dữ liệu gốc
        benchmark_to_process = json.loads(json.dumps(filtered_benchmark))

        # TỰ ĐỘNG LÀM SẠCH BENCHMARK TRƯỚC KHI GỬI ĐẾN AI
        # Điều này buộc AI phải phân tích từ đầu dựa trên test case được cung cấp.
        for section in benchmark_to_process.get("evaluationSections", []):
            for criterion in section.get("criteria", []):
                criterion["compliance"] = False
                criterion["comments"] = ""
        # ==================== KẾT THÚC PHẦN SỬA ĐỔI ====================

        try:
            # Sử dụng benchmark_to_process đã được làm sạch
            prompt = COVERAGE_ANALYSIS_PROMPT.format(
                benchmark_json=json.dumps(benchmark_to_process, indent=2, ensure_ascii=False),
                all_test_cases_json=json.dumps(all_sub_test_cases, indent=2, ensure_ascii=False)
            )
        except KeyError as e:
            logging.error(f"[{self.feature_name}] KeyError in prompt template: {e}. Expected keys are 'benchmark_json' and 'all_test_cases_json'. Please ensure prompt_storing_2.py is correct.")
            return None
        
        logging.info(f"[{self.feature_name}] Sending request to Gemini for analysis...")
        # (Phần còn lại của hàm giữ nguyên)
        try:
            response_text = self._evaluate_with_retry(prompt)
            result_json_str = response_text.strip()
            result_json_str = ftfy.fix_text(result_json_str)
            raw_response_path = os.path.join(self.output_dir, f"{self.feature_name}_raw_response.txt")
            with open(raw_response_path, 'w', encoding='utf-8') as f:
                f.write(result_json_str)
            logging.info(f"[{self.feature_name}] Raw response saved to {raw_response_path}")
            
            try:
                analysis_result = json.loads(result_json_str)
            except json.JSONDecodeError as json_err:
                logging.error(f"[{self.feature_name}] JSONDecodeError: {json_err}")
                logging.error(f"[{self.feature_name}] Problematic JSON snippet: {result_json_str[max(0, json_err.pos-50):json_err.pos+50]}")
                # Cố gắng sửa lỗi JSON phổ biến (dấu phẩy cuối)
                clean_json_str = re.sub(r",\s*([}\]])", r"\1", result_json_str)
                try:
                    analysis_result = json.loads(clean_json_str)
                    logging.info(f"[{self.feature_name}] Successfully parsed JSON after automatically fixing trailing comma.")
                except json.JSONDecodeError:
                    logging.error(f"[{self.feature_name}] Automatic JSON fix failed. The response is not valid JSON.")
                    return None
            
            feature_response_path = os.path.join(self.output_dir, f"{self.feature_name}_coverage_response.json")
            with open(feature_response_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, indent=2, ensure_ascii=False)
            logging.info(f"[{self.feature_name}] Detailed analysis response saved.")

            summary_data = self.generate_feature_report(analysis_result)
            return summary_data
        except Exception as e:
            logging.error(f"[{self.feature_name}] An error occurred during Gemini API call or processing: {e}", exc_info=True)
            return None     

    def generate_feature_report(self, analysis_result: Dict) -> Dict:
        report_lines = [
            f"================================================\n"
            f"      COVERAGE ANALYSIS: FEATURE '{self.feature_name}'\n"
            f"================================================\n"
            f"Analysis Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        ]
        
        covered_count, total_count = 0, 0
        individual_feature_gaps_text = []
        covered_items_text = []

        for section in analysis_result.get("evaluationSections", []):
            section_id = section.get("sectionId", "")
            for criterion in section.get("criteria", []):
                total_count += 1
                criterion_id = criterion.get('id', 'UNKNOWN_ID')
                criterion_description = criterion.get('description', 'No description provided.')
                comments = criterion.get('comments', '').replace("Covered by: ", "")

                if criterion.get("compliance") is True:
                    covered_count += 1
                    covered_items_text.append(f"[✓] {criterion_id}: {criterion_description}\n    - Covered by: {comments}")
                else:
                    # Xử lý tiêu chí boundary value
                    if 'boundary value' in criterion_description.lower():
                        fields, conditions = parse_boundary_criterion(criterion_description)
                        if fields and conditions:
                            # Trích xuất các trường hợp đã bao phủ từ comments
                            covered_cases = {}
                            for comment in comments.split(', '):
                                if 'GAP' not in comment:
                                    match = re.search(r'(\w+):\s*(\w+\s\w+|\w+)', comment)
                                    if match:
                                        field, cond = match.groups()
                                        covered_cases[field] = covered_cases.get(field, set()).union({cond})

                            # Kiểm tra từng trường và điều kiện
                            for field in fields:
                                field_covered = covered_cases.get(field, set())
                                for cond in conditions:
                                    if cond not in field_covered:
                                        individual_feature_gaps_text.append(f"[X] {criterion_id}: Boundary value tests for {field} ({cond})")
                                    else:
                                        individual_feature_gaps_text.append(f"[✓] {criterion_id}: Boundary value tests for {field} ({cond})\n    - Covered by: {', '.join([c for c in comments.split(', ') if field in c])}")
                        else:
                            individual_feature_gaps_text.append(f"[X] {criterion_id}: {criterion_description}")
                    else:
                        individual_feature_gaps_text.append(f"[X] {criterion_id}: {criterion_description}")
        
        coverage_percent = (covered_count / total_count * 100) if total_count > 0 else 0
        report_lines.append(f"--- SUMMARY ---\nOverall Coverage: {coverage_percent:.1f}% ({covered_count} / {total_count} criteria covered)\n")
        report_lines.append(f"--- GAPS (MISSING TESTS FOR THIS FEATURE) ---\n" + ('\n'.join(sorted(individual_feature_gaps_text)) if individual_feature_gaps_text else "No gaps found for this feature.") + "\n")
        report_lines.append(f"--- COVERED CRITERIA FOR THIS FEATURE ---\n" + ('\n'.join(sorted(covered_items_text)) if covered_items_text else "No criteria covered by this feature."))
        
        report_path = os.path.join(self.output_dir, f"{self.feature_name}_coverage_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        logging.info(f"[{self.feature_name}] Report saved.")
        
        # Collect covered criteria IDs
        covered_criteria_ids = []
        for section in analysis_result.get("evaluationSections", []):
            for criterion in section.get("criteria", []):
                if criterion.get("compliance") is True:
                    covered_criteria_ids.append(criterion.get('id'))
        
        return {
            "feature": self.feature_name,
            "coverage_percent": coverage_percent,
            "covered": covered_count,
            "total": total_count,
            "covered_criteria": covered_criteria_ids
        }

class TestSuiteEvaluator:
    """Orchestrator class: Discovers and evaluates all feature test suites."""
    def __init__(self, input_dir: str, benchmark_file: str, config_file: str, output_dir: Optional[str] = None, max_workers: int = 10):
        normalized_input_dir = os.path.normpath(input_dir)
        self.input_dir = os.path.abspath(normalized_input_dir)
        self.benchmark_file = os.path.abspath(benchmark_file)
        self.config_file = os.path.abspath(config_file)
        
        if output_dir:
            self.output_dir = os.path.abspath(output_dir)
        else:
            self.output_dir = os.path.join(os.path.dirname(self.input_dir), 'evaluation_output')

        os.makedirs(self.output_dir, exist_ok=True)
        self.config = self._load_json(self.config_file)
        
        self.full_benchmark_data = self._load_json(self.benchmark_file)
        self.all_benchmark_criteria_map: Dict[str, Dict] = {}
        if self.full_benchmark_data:
            for section in self.full_benchmark_data.get("evaluationSections", []):
                for criterion in section.get("criteria", []):
                    self.all_benchmark_criteria_map[criterion["id"]] = criterion
        logging.info(f"Loaded {len(self.all_benchmark_criteria_map)} total criteria from global benchmark.")

        # Thread-safe locks
        self.report_lock = threading.Lock()
        self.covered_criterion_lock = threading.Lock()
        self.max_workers = max_workers

    def _load_json(self, file_path: str) -> Optional[Dict]:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"Failed to load master config from {file_path}: {e}")
            return None
    
    def discover_features(self) -> List[Dict[str, Any]]:
        """
        Discovers feature(s) to evaluate.
        - If input_dir contains test files directly, it's treated as a single feature.
        - Otherwise, it scans for subdirectories which are treated as individual features.
        """
        feature_suites = []
        if not os.path.isdir(self.input_dir):
            logging.error(f"Input directory not found: {self.input_dir}")
            return []

        direct_test_files = glob.glob(os.path.join(self.input_dir, '*.csv')) + \
                            glob.glob(os.path.join(self.input_dir, '*.json'))

        if direct_test_files:
            logging.info(f"Input directory '{os.path.basename(self.input_dir)}' contains test files directly. Treating as a single feature.")
            feature_suites.append({
                "name": os.path.basename(self.input_dir),
                "test_files": direct_test_files
            })
            return feature_suites

        logging.info(f"Input directory is a root suite. Scanning for sub-feature directories...")
        for feature_name in os.listdir(self.input_dir):
            feature_dir = os.path.join(self.input_dir, feature_name)
            if os.path.isdir(feature_dir):
                test_files = glob.glob(os.path.join(feature_dir, '*.csv')) + \
                            glob.glob(os.path.join(feature_dir, '*.json'))
                if test_files:
                    feature_suites.append({
                        "name": feature_name,
                        "test_files": test_files
                    })
                else:
                    logging.warning(f"Skipping subdirectory '{feature_name}': No test files (.csv, .json) found.")
        
        return feature_suites

    def _process_feature_worker(self, feature_info: dict) -> dict:
        """Worker function để xử lý một feature trong thread."""
        feature_name = feature_info['name']
        
        result = {
            'feature_name': feature_name,
            'success': False,
            'error': None,
            'report_data': None,
            'covered_criteria': set()
        }
        
        try:
            logging.info(f"\n{'='*20} PROCESSING FEATURE: {feature_name.upper()} {'='*20}")
            
            analyzer = CoverageAnalyzer(
                feature_name=feature_name,
                test_case_files=feature_info['test_files'],  # Changed from 'files' to 'test_files'
                benchmark_file=self.benchmark_file,
                config=self.config,
                output_dir=os.path.join(self.output_dir, feature_name),
                config_path=self.config_file
            )
            
            # Run analysis (this is already async, but we'll call it synchronously in thread)
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            analysis_result = loop.run_until_complete(analyzer.run_analysis())
            loop.close()
            
            if analysis_result:
                result['success'] = True
                result['report_data'] = analysis_result
                result['covered_criteria'] = set(analysis_result.get('covered_criteria', []))
                logging.info(f"[SUCCESS] {feature_name} analysis completed successfully")  # Removed emoji
            else:
                result['error'] = "Analysis returned None"
                logging.error(f"[FAILED] {feature_name} analysis failed")  # Removed emoji
            
        except Exception as e:
            result['error'] = str(e)
            logging.error(f"[ERROR] {feature_name} failed with exception: {e}")  # Removed emoji
        
        return result

    async def run_all(self):
        """Runs the evaluation for all discovered feature suites with multithreading."""
        if not self.config:
            logging.error("Master configuration failed to load. Aborting.")
            return
        if not self.full_benchmark_data:
            logging.error("Global benchmark data failed to load. Aborting.")
            return

        features = self.discover_features()
        if not features:
            logging.warning("No valid feature suites found to evaluate.")
            return
        
        logging.info(f"Discovered {len(features)} feature suites to evaluate: {[f['name'] for f in features]}")
        logging.info(f"Starting parallel processing with {self.max_workers} workers")
        
        master_report_data = []
        covered_criterion_ids: Set[str] = set()
        successful_features = 0
        failed_features = 0
        
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_feature = {
                executor.submit(self._process_feature_worker, feature_info): feature_info['name']
                for feature_info in features
            }
            
            # Process completed tasks
            for future in as_completed(future_to_feature):
                feature_name = future_to_feature[future]
                try:
                    result = future.result()
                    
                    if result['success']:
                        successful_features += 1
                        
                        # Thread-safe update of shared data
                        with self.report_lock:
                            master_report_data.append(result['report_data'])
                        
                        with self.covered_criterion_lock:
                            covered_criterion_ids.update(result['covered_criteria'])
                            
                    else:
                        failed_features += 1
                        error_msg = result.get('error', 'Unknown error')
                        logging.error(f"[FAILED] {feature_name} failed: {error_msg}")  # Removed emoji
                    
                except Exception as e:
                    failed_features += 1
                    logging.error(f"[ERROR] {feature_name} failed with exception: {e}")  # Removed emoji
        
        # Generate master summary report
        logging.info(f"\nProcessing completed: {successful_features} successful, {failed_features} failed")
        
        # Calculate missing criteria
        final_missing_criteria_list = []
        for criterion_id, criterion_data in self.all_benchmark_criteria_map.items():
            if criterion_id not in covered_criterion_ids:
                final_missing_criteria_list.append({
                    'id': criterion_id,
                    'description': criterion_data.get('description', 'No description'),
                    'comments': criterion_data.get('comments', '')
                })
        
        # Use the existing generate_master_report method
        self.generate_master_report(master_report_data, final_missing_criteria_list, covered_criterion_ids)

    def generate_master_report(self, report_data: List[Dict], final_missing_criteria_list: List[Dict], covered_criterion_ids: Set[str]):
        """Generates a top-level summary report for all evaluated features."""
        report_lines = [
            "================================================",
            "          MASTER COVERAGE SUMMARY REPORT          ",
            "================================================",
            f"Evaluation Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Benchmark Used: {os.path.basename(self.benchmark_file)}\n"
        ]
        
        if not report_data:
            report_lines.append("No features were successfully evaluated.")
        else:
            total_criteria_in_benchmark = len(self.all_benchmark_criteria_map)
            overall_covered_count = len(covered_criterion_ids)
            
            overall_percent = (overall_covered_count / total_criteria_in_benchmark * 100) if total_criteria_in_benchmark > 0 else 0
            report_lines.append(f"Overall Project Coverage: {overall_percent:.1f}% ({overall_covered_count} / {total_criteria_in_benchmark} criteria covered)\n")
            report_lines.append("--- COVERAGE BY FEATURE ---")
            
            for item in sorted(report_data, key=lambda x: x['coverage_percent']):
                report_lines.append(f"  - {item['feature']}: {item['coverage_percent']:.1f}% ({item['covered']}/{item['total']})")

            report_lines.append("\n--- OVERALL MISSING CRITERIA (GAPS ACROSS PROJECT) ---")
            if final_missing_criteria_list:
                for mc in final_missing_criteria_list:
                    if 'boundary value' in mc['description'].lower():
                        fields, conditions = parse_boundary_criterion(mc['description'])
                        if fields and conditions:
                            for field in fields:
                                for cond in conditions:
                                    if f"{field}: {cond}" not in mc.get('comments', ''):
                                        report_lines.append(f"- {mc['id']}: Boundary value tests for {field} ({cond})")
                        else:
                            report_lines.append(f"- {mc['id']}: {mc['description']}")
                    else:
                        report_lines.append(f"- {mc['id']}: {mc['description']}")
            else:
                report_lines.append("No missing criteria found across the entire project. All benchmark criteria are covered!")

        report_lines.append("\n\n--- END OF MASTER REPORT ---")
        
        report_path = os.path.join(self.output_dir, "master_summary_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(report_lines))
        logging.info(f"Master summary report saved to: {report_path}")

async def main():
    setup_logging()

    # Hardcoded parameters with max_workers option
    input_dir = "test_suites\\E-Commerce_version2"
    benchmark_file = "evaluation_benchmark_4.json"
    config_file = "gemini_config_flash.json"
    output_dir = "evaluation_output\\E-Commerce_version2"
    max_workers = 10  # Number of threads

    parser = argparse.ArgumentParser(description="Analyze multiple test suites for full project coverage.")
    parser.add_argument("--input-dir", default=input_dir, help="Root directory containing feature folders.")
    parser.add_argument("--benchmark", default=benchmark_file, help="Path to the SINGLE, GLOBAL coverage checklist file.")
    parser.add_argument("--config", default=config_file, help="Path to the configuration file.")
    parser.add_argument("--output-dir", default=output_dir, help="Root directory to save all evaluation reports.")
    parser.add_argument("--max-workers", type=int, default=max_workers, help="Maximum number of worker threads.")

    args = parser.parse_args()

    evaluator = TestSuiteEvaluator(
        input_dir=args.input_dir,
        benchmark_file=args.benchmark,
        config_file=args.config,
        output_dir=args.output_dir,
        max_workers=args.max_workers
    )

    await evaluator.run_all()
    logging.info("Full test suite evaluation process finished.")

if __name__ == "__main__":
    asyncio.run(main())
