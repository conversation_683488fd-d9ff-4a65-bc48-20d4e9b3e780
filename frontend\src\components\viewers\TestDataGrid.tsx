import React, { useState, useMemo } from 'react'
import { AgGridReact } from 'ag-grid-react'
import { ColDef, GridReadyEvent } from 'ag-grid-community'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'
import { Download, Filter, Search } from 'lucide-react'

interface TestDataGridProps {
  data: string | any[]
  fileName: string
}

export const TestDataGrid: React.FC<TestDataGridProps> = ({ data, fileName }) => {
  const [searchText, setSearchText] = useState('')
  const [gridApi, setGridApi] = useState<any>(null)

  // Parse CSV data if it's a string
  const parsedData = useMemo(() => {
    if (typeof data === 'string') {
      const lines = data.trim().split('\n')
      if (lines.length === 0) return []

      const headers = lines[0].split(',').map(h => h.trim())
      const rows = lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.trim())
        const row: any = {}
        headers.forEach((header, index) => {
          row[header] = values[index] || ''
        })
        return row
      })

      return rows
    }
    return Array.isArray(data) ? data : []
  }, [data])

  // Generate column definitions
  const columnDefs = useMemo((): ColDef[] => {
    if (parsedData.length === 0) return []

    const firstRow = parsedData[0]
    return Object.keys(firstRow).map(key => ({
      field: key,
      headerName: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      sortable: true,
      filter: true,
      resizable: true,
      flex: 1,
      minWidth: 100,
      cellStyle: { color: '#ffffff', backgroundColor: 'transparent' },
      headerClass: 'ag-header-cell-dark',
    }))
  }, [parsedData])

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api)
  }

  const onSearchChange = (value: string) => {
    setSearchText(value)
    if (gridApi) {
      gridApi.setQuickFilter(value)
    }
  }

  const exportToCsv = () => {
    if (gridApi) {
      gridApi.exportDataAsCsv({
        fileName: fileName.replace('.csv', '_exported.csv'),
      })
    }
  }

  const clearFilters = () => {
    if (gridApi) {
      gridApi.setFilterModel(null)
      setSearchText('')
      gridApi.setQuickFilter('')
    }
  }

  if (parsedData.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-dark-400 mb-2">No data available</p>
          <p className="text-xs text-dark-500">
            The file might be empty or in an unsupported format
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-dark-950">
      {/* Toolbar */}
      <div className="p-4 border-b border-dark-700 bg-dark-900">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-white">{fileName}</h3>
            <p className="text-xs text-dark-300 mt-1">
              {parsedData.length} rows, {columnDefs.length} columns
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-dark-400" />
              <input
                type="text"
                placeholder="Search data..."
                value={searchText}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 text-white rounded text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 w-64"
              />
            </div>

            {/* Clear Filters */}
            <button
              onClick={clearFilters}
              className="flex items-center space-x-2 px-3 py-2 bg-dark-700 hover:bg-dark-600 text-white rounded text-sm transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span>Clear</span>
            </button>

            {/* Export */}
            <button
              onClick={exportToCsv}
              className="flex items-center space-x-2 px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded text-sm transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Data Grid */}
      <div className="flex-1 ag-theme-alpine" style={{ '--ag-background-color': '#111827', '--ag-header-background-color': '#1f2937', '--ag-odd-row-background-color': '#1f2937', '--ag-row-hover-color': '#374151', '--ag-border-color': '#4b5563', '--ag-foreground-color': '#ffffff', '--ag-secondary-foreground-color': '#d1d5db' } as any}>
        <AgGridReact
          rowData={parsedData}
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          defaultColDef={{
            sortable: true,
            filter: true,
            resizable: true,
          }}
          animateRows={true}
          rowSelection="multiple"
          enableRangeSelection={true}
          pagination={true}
          paginationPageSize={50}
          suppressMenuHide={true}
          enableCellTextSelection={true}
          ensureDomOrder={true}
          className="text-white"
        />
      </div>

      {/* Status Bar */}
      <div className="p-2 border-t border-dark-700 bg-dark-900">
        <div className="flex items-center justify-between text-xs text-dark-400">
          <span>
            Showing {parsedData.length} rows
          </span>
          <span>
            Use filters and search to explore the data
          </span>
        </div>
      </div>
    </div>
  )
}
