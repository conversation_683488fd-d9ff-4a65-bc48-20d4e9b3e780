"""
Alternative startup script for the FastAPI backend
This script ensures proper module resolution
"""
import uvicorn
import os
import sys
from pathlib import Path

# Get the backend directory
backend_dir = Path(__file__).parent.absolute()
project_root = backend_dir.parent

# Add both backend and project root to Python path
sys.path.insert(0, str(backend_dir))
sys.path.insert(0, str(project_root))

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = f"{backend_dir}{os.pathsep}{project_root}"

if __name__ == "__main__":
    print(f"Starting server from: {backend_dir}")
    print(f"Python path includes: {backend_dir}")
    
    try:
        # Import the app to test if modules can be found
        from app.main import app
        print("✅ Successfully imported app")
        
        # Start the server
        uvicorn.run(
            "app.main:app",
            host="127.0.0.1",  # Bind to IPv4 localhost specifically
            port=8000,
            reload=True,
            log_level="info"
        )
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please check that you're running this script from the backend directory")
        sys.exit(1)
