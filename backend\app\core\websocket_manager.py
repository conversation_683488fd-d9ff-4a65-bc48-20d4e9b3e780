"""
Production-ready WebSocket Connection Manager for real-time logging
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from collections import defaultdict
import uuid

logger = logging.getLogger(__name__)

class ConnectionManager:
    """
    Robust WebSocket connection manager for real-time logging system.
    Handles multiple clients, run-specific subscriptions, and broadcasting.
    """

    def __init__(self):
        # Active connections grouped by run_id
        self.active_connections: Dict[str, Set[WebSocket]] = defaultdict(set)
        # Connection metadata for debugging and monitoring
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}
        # Message queue for offline clients (optional feature)
        self.message_queues: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        # Connection statistics
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "errors": 0
        }

    async def connect(self, websocket: WebSocket, run_id: str, client_id: Optional[str] = None) -> str:
        """
        Accept a new WebSocket connection and register it for a specific run_id.

        Args:
            websocket: The WebSocket connection
            run_id: The pipeline run ID to subscribe to
            client_id: Optional client identifier

        Returns:
            str: Generated client_id for this connection
        """
        try:
            await websocket.accept()

            # Generate client_id if not provided
            if not client_id:
                client_id = str(uuid.uuid4())[:8]

            # Register connection
            self.active_connections[run_id].add(websocket)
            self.connection_metadata[websocket] = {
                "run_id": run_id,
                "client_id": client_id,
                "connected_at": datetime.utcnow().isoformat(),
                "messages_received": 0
            }

            # Update stats
            self.stats["total_connections"] += 1
            self.stats["active_connections"] = sum(len(conns) for conns in self.active_connections.values())

            logger.info(f"WebSocket connected: client_id={client_id}, run_id={run_id}")

            # Send connection confirmation
            await self._send_to_websocket(websocket, {
                "type": "connection_established",
                "client_id": client_id,
                "run_id": run_id,
                "timestamp": datetime.utcnow().isoformat()
            })

            # Send any queued messages for this run_id
            await self._send_queued_messages(websocket, run_id)

            return client_id

        except Exception as e:
            logger.error(f"Error connecting WebSocket: {e}")
            self.stats["errors"] += 1
            raise

    async def disconnect(self, websocket: WebSocket):
        """
        Handle WebSocket disconnection and cleanup.

        Args:
            websocket: The WebSocket connection to disconnect
        """
        try:
            metadata = self.connection_metadata.get(websocket, {})
            run_id = metadata.get("run_id")
            client_id = metadata.get("client_id", "unknown")

            # Remove from active connections
            if run_id and websocket in self.active_connections[run_id]:
                self.active_connections[run_id].remove(websocket)

                # Clean up empty run_id entries
                if not self.active_connections[run_id]:
                    del self.active_connections[run_id]

            # Remove metadata
            if websocket in self.connection_metadata:
                del self.connection_metadata[websocket]

            # Update stats
            self.stats["active_connections"] = sum(len(conns) for conns in self.active_connections.values())

            logger.info(f"WebSocket disconnected: client_id={client_id}, run_id={run_id}")

        except Exception as e:
            logger.error(f"Error disconnecting WebSocket: {e}")
            self.stats["errors"] += 1

    async def broadcast_to_run(self, run_id: str, message: Dict[str, Any]):
        """
        Broadcast a message to all clients subscribed to a specific run_id.

        Args:
            run_id: The pipeline run ID
            message: The message to broadcast
        """
        if run_id not in self.active_connections:
            # Queue message for future connections
            self.message_queues[run_id].append(message)
            logger.debug(f"No active connections for run_id={run_id}, message queued")
            return

        connections = self.active_connections[run_id].copy()  # Copy to avoid modification during iteration
        disconnected_connections = []

        for websocket in connections:
            try:
                await self._send_to_websocket(websocket, message)
                self.stats["messages_sent"] += 1

            except WebSocketDisconnect:
                disconnected_connections.append(websocket)
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {e}")
                disconnected_connections.append(websocket)
                self.stats["errors"] += 1

        # Clean up disconnected connections
        for websocket in disconnected_connections:
            await self.disconnect(websocket)

    async def broadcast_log(self, run_id: str, log_data: dict):
        """
        Send a structured log message to all clients subscribed to a run_id.

        This is the standardized method for broadcasting log messages to WebSocket clients.

        Args:
            run_id: The pipeline run ID
            log_data: Dictionary containing log information (level, message, step_id, etc.)
        """
        # Ensure required fields and add timestamp
        log_message = {
            "type": "log",
            "timestamp": datetime.now().isoformat(),
            "run_id": run_id,
            **log_data  # Merge provided log data
        }

        await self.broadcast_to_run(run_id, log_message)

    # Convenience method for backward compatibility
    async def broadcast_log_simple(self, run_id: str, level: str, message: str, step_id: str, metadata: Optional[Dict[str, Any]] = None):
        """Convenience method for simple log broadcasting"""
        log_data = {
            "level": level,
            "message": message,
            "step_id": step_id,
            "metadata": metadata or {}
        }
        await self.broadcast_log(run_id, log_data)

    # Backward compatibility alias
    async def log(self, run_id: str, level: str, message: str, step_id: str, metadata: Optional[Dict[str, Any]] = None):
        """Backward compatibility alias for broadcast_log"""
        await self.broadcast_log_simple(run_id, level, message, step_id, metadata)

    # Additional alias for send_log calls
    async def send_log(self, run_id: str, level: str, message: str, step_id: str, metadata: Optional[Dict[str, Any]] = None):
        """Alias for broadcast_log to handle send_log calls"""
        await self.broadcast_log_simple(run_id, level, message, step_id, metadata)

    async def send_step_update(self, run_id: str, step_id: str, status: str, progress: Optional[float] = None):
        """
        Send a step status update to all clients subscribed to a run_id.

        Args:
            run_id: The pipeline run ID
            step_id: The pipeline step identifier
            status: The step status (running, completed, failed)
            progress: Optional progress percentage (0-100)
        """
        update_message = {
            "type": "step_update",
            "timestamp": datetime.now().isoformat(),
            "step_id": step_id,
            "status": status,
            "progress": progress,
            "run_id": run_id
        }

        logger.info(f"🚀 Sending step_update: {step_id} -> {status} (run_id: {run_id})")
        await self.broadcast_to_run(run_id, update_message)
        logger.info(f"✅ Step_update sent successfully")

    # Backward compatibility aliases for terminal_logger
    async def send_pipeline_update(self, step: str, status: str, progress: Optional[float] = None):
        """Backward compatibility alias for send_step_update"""
        await self.send_step_update("default", step, status, progress)

    async def send_status(self, status: str, details: Optional[str] = None):
        """Send status update as a log message"""
        await self.broadcast_log("default", {
            "level": "info",
            "message": f"Status: {status}" + (f" - {details}" if details else ""),
            "step_id": "system",
            "metadata": {"type": "status", "status": status, "details": details}
        })

    async def _send_to_websocket(self, websocket: WebSocket, message: Dict[str, Any]):
        """
        Send a message to a specific WebSocket connection.

        Args:
            websocket: The WebSocket connection
            message: The message to send
        """
        try:
            await websocket.send_text(json.dumps(message))

            # Update connection metadata
            if websocket in self.connection_metadata:
                self.connection_metadata[websocket]["messages_received"] += 1

        except Exception as e:
            logger.error(f"Failed to send message to WebSocket: {e}")
            raise

    async def _send_queued_messages(self, websocket: WebSocket, run_id: str):
        """
        Send any queued messages to a newly connected client.

        Args:
            websocket: The WebSocket connection
            run_id: The pipeline run ID
        """
        if run_id in self.message_queues:
            queued_messages = self.message_queues[run_id]

            for message in queued_messages:
                try:
                    await self._send_to_websocket(websocket, message)
                except Exception as e:
                    logger.error(f"Failed to send queued message: {e}")
                    break

    def get_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            **self.stats,
            "active_runs": len(self.active_connections),
            "connections_by_run": {run_id: len(conns) for run_id, conns in self.active_connections.items()}
        }

# Global connection manager instance
connection_manager = ConnectionManager()

# Backward compatibility with existing code
websocket_manager = connection_manager