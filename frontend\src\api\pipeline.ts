/**
 * API functions for pipeline operations
 */

const API_BASE = '/api/v1'

export interface PipelineGraphResponse {
  nodes: Array<{
    id: string
    type: string
    position: { x: number; y: number }
    data: {
      label: string
      description: string
      status: string
      inputs: string[]
      outputs: string[]
      parameters: Record<string, any>
      configurable_params: Record<string, any>
    }
  }>
  edges: Array<{
    id: string
    source: string
    target: string
    type: string
    animated: boolean
    data?: { artifact: string }
  }>
  metadata: {
    total_steps: number
    total_connections: number
  }
}

export interface ExecuteStepRequest {
  parameters: Record<string, any>
  session_id: string
}

export interface ExecuteStepResponse {
  success: boolean
  step_id: string
  result?: Record<string, any>
  error?: string
  outputs?: Record<string, any>
}

export async function fetchPipelineGraph(): Promise<PipelineGraphResponse> {
  const response = await fetch(`${API_BASE}/pipeline/graph`)
  if (!response.ok) {
    throw new Error(`Failed to fetch pipeline graph: ${response.statusText}`)
  }
  return response.json()
}

export async function executeStep(
  stepId: string,
  request: ExecuteStepRequest
): Promise<ExecuteStepResponse> {
  const response = await fetch(`${API_BASE}/pipeline/steps/${stepId}/execute`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  })

  if (!response.ok) {
    throw new Error(`Failed to execute step: ${response.statusText}`)
  }

  return response.json()
}

export async function stopStepExecution(
  stepId: string, 
  sessionId: string = 'default'
): Promise<{ success: boolean; step_id: string }> {
  const response = await fetch(`${API_BASE}/stop/${stepId}?session_id=${sessionId}`, {
    method: 'POST',
  })
  
  if (!response.ok) {
    throw new Error(`Failed to stop step execution: ${response.statusText}`)
  }
  
  return response.json()
}

export async function getFileContent(
  path: string, 
  sessionId: string = 'default'
): Promise<{ type: string; content: any; extension?: string }> {
  const response = await fetch(
    `${API_BASE}/files?path=${encodeURIComponent(path)}&session_id=${sessionId}`
  )
  
  if (!response.ok) {
    throw new Error(`Failed to get file content: ${response.statusText}`)
  }
  
  const contentType = response.headers.get('content-type')
  
  if (contentType?.includes('application/json')) {
    return response.json()
  } else if (contentType?.includes('image/')) {
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    return { type: 'image', content: url }
  } else {
    const text = await response.text()
    return { type: 'text', content: text }
  }
}

export async function listWorkspaceFiles(
  sessionId: string = 'default',
  subdirectory: string = ''
): Promise<{
  files: Array<{
    name: string
    path: string
    size: number
    modified: number
  }>
  directories: Array<{
    name: string
    path: string
  }>
}> {
  const params = new URLSearchParams({
    session_id: sessionId,
    ...(subdirectory && { subdirectory }),
  })
  
  const response = await fetch(`${API_BASE}/workspace/files?${params}`)
  
  if (!response.ok) {
    throw new Error(`Failed to list workspace files: ${response.statusText}`)
  }
  
  return response.json()
}

export async function getStepDetails(stepId: string): Promise<any> {
  const response = await fetch(`${API_BASE}/step/${stepId}`)
  
  if (!response.ok) {
    throw new Error(`Failed to get step details: ${response.statusText}`)
  }
  
  return response.json()
}

export async function getSessionStatus(sessionId: string): Promise<{
  session_id: string
  step_statuses: Record<string, string>
}> {
  const response = await fetch(`${API_BASE}/session/${sessionId}/status`)
  
  if (!response.ok) {
    throw new Error(`Failed to get session status: ${response.statusText}`)
  }
  
  return response.json()
}
