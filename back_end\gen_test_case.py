import json
import os
import google.generativeai as generativeai
from google import genai
from google.genai import types
from . import prompt_storing # Assuming this exists and has gen_tc_pairwise_prompt
import pandas as pd
from .rotate_api_key import APIKeyRotator # Import the class directly
from google.api_core.exceptions import ResourceExhausted
from .count_gemini_token import GeminiCostCalculator
from .billing_logger import billing_logger
import csv # For CSV reading
import traceback # For detailed error logging
import threading # For multithreading support
import time # For timing measurements
# import time # Uncomment if you need delays

class GenTestCaseBussinessFlow:
    """
    Generates test case JSON files from CSV files.

    Supports both single business flow processing (backward compatibility) and
    multiple business flow processing (new structure).

    New Structure Usage:
        # Process all business flows automatically
        results = GenTestCaseBussinessFlow.process_from_base_dir(
            base_dir="path/to/base",
            output_dir="path/to/output"
        )

        # Or process specific business flow
        generator = GenTestCaseBussinessFlow.create_for_business_flow(
            test_case_generator_base_dir="path/to/test_case_generator",
            business_flows_dir="path/to/business_flows",
            business_flow_number=1,
            base_output_dir="path/to/output"
        )
        generator.execute()

    Old Structure Usage (backward compatibility):
        generator = GenTestCaseBussinessFlow(
            csv_file="path/to/testcase.csv",
            output_directory="path/to/output",
            use_case="business flow text"
        )
        generator.execute()
    """

    # --- __init__, load_config, _rotate_and_reconfigure_api, _attempt_generate_content ---
    def __init__(self, csv_file, output_directory, input_data=None, config_file="back_end/gemini_config.json",
                 use_case=None, count_token=False, business_flow_number=None):
        self.csv_file = csv_file
        self.output_directory = output_directory
        self.input_data = input_data
        self.base_filename = os.path.splitext(os.path.basename(csv_file))[0]
        self.use_case = use_case
        self.count_token = count_token
        self.business_flow_number = business_flow_number
        
        # Initialize token tracking
        self.cost_calculator = GeminiCostCalculator() if count_token else None
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_cost = 0.0
        self.model_type = None  # Will be set based on config
        self.api_call_count = 0

        self.config = None
        self.rotator = None
        self.api_key = None
        self.model_name = None
        self.response_mime_type = None
        self.model = None
        self.initialization_ok = False
        self.relevant_info_cache = None
        self.client = None

        # Threading support
        self.results_lock = threading.Lock()
        self.results = {}

        # Test case ID counter for unique IDs
        self.test_case_counter = 0
        self.counter_lock = threading.Lock()

        # Token counting lock
        self.token_lock = threading.Lock()

        try:
            self.config = self.load_config(config_file)
            if self.config:
                # Determine model type for token calculation
                if self.count_token:
                    self._determine_model_type()
                    
                self.rotator = APIKeyRotator(config_path=config_file)  # Create APIKeyRotator instance directly
                if not self.rotator.api_keys:
                     print("Error: APIKeyRotator failed to load any keys.")
                     return

                self.api_key = self.rotator.get_api_key()
                # Ensure keys exist in config before accessing
                gemini_config = self.config.get("config_gemini_model", {})
                self.model_name = gemini_config.get("model_name", "gemini-2.5-pro")
                self.response_mime_type = gemini_config.get("response_mime_type")

                if not self.model_name or not self.response_mime_type:
                     print("Error: Missing 'model_name' or 'response_mime_type' in config_gemini_model.")
                     return

                self._configure_client_and_cache()
                self.initialization_ok = True

                print(f"Initialization successful. Using API key ending with: ...{self.api_key[-4:]}")
                if self.count_token:
                    print(f"Token counting enabled for model type: {self.model_type}")
            else:
                print("Failed to load configuration. Initialization aborted.")

        except Exception as e:
            print(f"An error occurred during initialization: {e}")
            print(traceback.format_exc())
            
    def _determine_model_type(self) -> None:
        """Xác định loại model (pro hay flash) dựa trên config file."""
        if not self.config:
            return
            
        model_name = self.config.get("config_gemini_model", {}).get("model_name", "").lower()
        
        if "flash" in model_name:
            self.model_type = "flash"
        elif "pro" in model_name:
            self.model_type = "pro"
        else:
            # Default to pro if unclear
            self.model_type = "pro"

    def _calculate_token_cost(self, input_tokens: int, output_tokens: int) -> dict:
        """
        Tính toán chi phí token dựa trên model type.
        
        Args:
            input_tokens: Số token đầu vào
            output_tokens: Số token đầu ra
            
        Returns:
            Dict chứa thông tin chi phí
        """
        if not self.cost_calculator or not self.model_type:
            return {}
            
        if self.model_type == "flash":
            return self.cost_calculator.calculate_flash_cost(input_tokens, output_tokens)
        else:
            return self.cost_calculator.calculate_pro_cost(input_tokens, output_tokens)
            
    def get_token_summary(self) -> dict:
        """
        Trả về tổng kết chi phí token.
        
        Returns:
            Dict chứa thông tin tổng kết về token và chi phí
        """
        if not self.count_token or not self.cost_calculator:
            return {"token_counting_enabled": False}
            
        total_cost_info = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens)
        
        return {
            "token_counting_enabled": True,
            "model_type": self.model_type,
            "total_input_tokens": self.total_input_tokens,
            "total_output_tokens": self.total_output_tokens,
            "total_tokens": self.total_input_tokens + self.total_output_tokens,
            "api_call_count": self.api_call_count,
            **total_cost_info
        }

    def print_token_summary(self) -> None:
        """In tổng kết chi phí token và cost."""
        if not self.count_token or not self.cost_calculator:
            return
            
        token_summary = self.get_token_summary()
        self.cost_calculator.print_token_summary(token_summary, "GEN TEST CASE")
        
        # Log billing information
        billing_logger.log_module_cost("GenTestCaseBussinessFlow", token_summary)

    def _configure_client_and_cache(self):
        """Configures the generativeai client, model, and finds the cache."""
        generativeai.configure(api_key=self.api_key)
        self.client = genai.Client(api_key=self.api_key)
        self.model = generativeai.GenerativeModel(
            self.model_name,
            generation_config={"response_mime_type": self.response_mime_type}
        )
        
        print("Attempting to find relevant_info cache...")
        cache_found = False
        try:
            for cache in self.client.caches.list():
                if cache.display_name == "relevant_info":
                    self.relevant_info_cache = cache
                    print(f"Receive cache successfully: {self.relevant_info_cache.name}")
                    cache_found = True
                    break
            if not cache_found:
                print("Warning: 'relevant_info' cache not found.")
        except Exception as e:
            print(f"Warning: Could not list cached contents. Caching may not work. Error: {e}")

    def load_config(self, file_path="gemini_config.json"):
        """Loads configuration from a JSON file."""
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                return json.load(file)
        except FileNotFoundError:
            print(f"Configuration file {file_path} not found.")
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from {file_path}: {e}")
        except Exception as e:
            print(f"An unexpected error occurred loading config {file_path}: {e}")
        return None

    @classmethod
    def create_for_business_flow(cls, test_case_generator_base_dir: str, business_flows_dir: str,
                                business_flow_number: int, base_output_dir: str,
                                config_file: str = "back_end/document/gemini_config_pro.json"):
        """
        Factory method to create GenTestCaseBussinessFlow for a specific business flow.

        Args:
            test_case_generator_base_dir: Base directory containing test case generator output (CSV files).
            business_flows_dir: Directory containing business flow text files.
            business_flow_number: Number of the business flow to process.
            base_output_dir: Base directory for output files.
            config_file: Path to Gemini config file.

        Returns:
            GenTestCaseBussinessFlow instance configured for the specified business flow.
        """
        # Load business flow text
        business_flow_path = os.path.join(business_flows_dir, f"Business Flow {business_flow_number}.txt")
        try:
            with open(business_flow_path, 'r', encoding='utf-8') as f:
                use_case = f.read()
        except FileNotFoundError:
            print(f"⚠️ Business flow file not found: {business_flow_path}")
            use_case = f"Business flow {business_flow_number}"

        # Create output directory for this business flow
        output_directory = os.path.join(base_output_dir, f"business_flow_{business_flow_number}")

        # Get CSV files directory for this business flow
        csv_dir = os.path.join(test_case_generator_base_dir, f"business_flow_{business_flow_number}")

        return {
            "csv_dir": csv_dir,
            "output_directory": output_directory,
            "use_case": use_case,
            "config_file": config_file,
            "business_flow_number": business_flow_number
        }

    @staticmethod
    def process_all_business_flows(test_case_generator_base_dir: str, business_flows_dir: str,
                                  base_output_dir: str, config_file: str = "back_end/document/gemini_config_pro.json",
                                  count_token: bool = True):
        """
        Process all business flows found in the test_case_generator_base_dir.

        Args:
            test_case_generator_base_dir: Base directory containing test case generator output.
            business_flows_dir: Directory containing business flow text files.
            base_output_dir: Base directory for output files.
            config_file: Path to Gemini config file.
            count_token: Whether to count tokens and calculate costs.

        Returns:
            List of results from processing each business flow.
        """
        results = []

        # Find all business_flow directories
        if not os.path.exists(test_case_generator_base_dir):
            print(f"❌ Test case generator base directory not found: {test_case_generator_base_dir}")
            return results

        business_flow_dirs = [d for d in os.listdir(test_case_generator_base_dir)
                             if d.startswith("business_flow_") and
                             os.path.isdir(os.path.join(test_case_generator_base_dir, d))]

        if not business_flow_dirs:
            print(f"No business flow directories found in {test_case_generator_base_dir}")
            return results

        business_flow_dirs.sort()  # Process in order

        for business_flow_dir in business_flow_dirs:
            # Extract business flow number
            import re
            match = re.search(r"business_flow_(\d+)", business_flow_dir)
            if not match:
                continue

            business_flow_number = int(match.group(1))
            print(f"\n{'='*50}")
            print(f"Processing Business Flow {business_flow_number} - Final Test Case Generation")
            print(f"{'='*50}")

            try:
                # Get configuration for this business flow
                flow_config = GenTestCaseBussinessFlow.create_for_business_flow(
                    test_case_generator_base_dir=test_case_generator_base_dir,
                    business_flows_dir=business_flows_dir,
                    business_flow_number=business_flow_number,
                    base_output_dir=base_output_dir,
                    config_file=config_file
                )

                # Create output directory
                os.makedirs(flow_config["output_directory"], exist_ok=True)

                # Get CSV files for this business flow
                csv_files = [f for f in os.listdir(flow_config["csv_dir"]) if f.lower().endswith(".csv")]

                if not csv_files:
                    print(f"⚠️ No CSV files found in {flow_config['csv_dir']}")
                    results.append({
                        "business_flow_number": business_flow_number,
                        "success": False,
                        "error": "No CSV files found"
                    })
                    continue

                print(f"📄 Found {len(csv_files)} CSV files to process")

                # Process each CSV file
                processed_files = []
                # Create a single generator instance for this business flow to maintain counter consistency
                generator = GenTestCaseBussinessFlow(
                    csv_file="",  # Will be set for each file
                    output_directory=flow_config["output_directory"],
                    config_file=flow_config["config_file"],
                    use_case=flow_config["use_case"],
                    count_token=count_token,
                    business_flow_number=business_flow_number
                )

                for filename in csv_files:
                    csv_path = os.path.join(flow_config["csv_dir"], filename)
                    print(f"🔄 Processing {filename}...")

                    # Update the CSV file path for this iteration
                    generator.csv_file = csv_path
                    generator.base_filename = os.path.splitext(os.path.basename(csv_path))[0]

                    test_cases = generator.execute()
                    if test_cases:
                        processed_files.append(filename)
                        print(f"✅ Successfully processed {filename}")
                    else:
                        print(f"⚠️ Failed to process {filename}")

                results.append({
                    "business_flow_number": business_flow_number,
                    "success": True,
                    "output_directory": flow_config["output_directory"],
                    "processed_files": processed_files,
                    "total_files": len(csv_files)
                })

                print(f"✅ Business Flow {business_flow_number} completed: {len(processed_files)}/{len(csv_files)} files processed")

            except Exception as e:
                print(f"❌ Error processing Business Flow {business_flow_number}: {e}")
                results.append({
                    "business_flow_number": business_flow_number,
                    "success": False,
                    "error": str(e)
                })

        # Print overall summary
        successful_flows = [r for r in results if r['success']]
        print(f"\n{'='*50}")
        print("FINAL TEST CASE GENERATION SUMMARY")
        print(f"{'='*50}")
        print(f"Total Business Flows Processed: {len(results)}")
        print(f"Successful: {len(successful_flows)}")
        print(f"Failed: {len(results) - len(successful_flows)}")

        if successful_flows:
            print(f"\n📁 Generated Test Cases:")
            for result in successful_flows:
                print(f"  Business Flow {result['business_flow_number']}: {result['output_directory']} ({result['processed_files']}/{result['total_files']} files)")

        return results

    @staticmethod
    def process_from_base_dir(base_dir: str, output_dir: str = None,
                             config_file: str = "back_end/document/gemini_config_pro.json",
                             count_token: bool = True):
        """
        Convenience method to process all business flows from a base directory.
        Automatically detects test_case_generator/ and business_flows/ subdirectories.

        Args:
            base_dir: Base directory containing test_case_generator/ and business_flows/
            output_dir: Output directory. If None, uses base_dir/TestCase_Output/
            config_file: Path to Gemini config file.
            count_token: Whether to count tokens and calculate costs.

        Returns:
            List of results from processing each business flow.
        """
        test_case_generator_base_dir = os.path.join(base_dir, "test_case_csv_generator")
        business_flows_dir = os.path.join(base_dir, "business_flows")

        if output_dir is None:
            output_dir = os.path.join(base_dir, "TestCase_Output")

        # Validate paths exist
        if not os.path.exists(test_case_generator_base_dir):
            print(f"❌ Test case generator directory not found: {test_case_generator_base_dir}")
            return []

        if not os.path.exists(business_flows_dir):
            print(f"❌ Business flows directory not found: {business_flows_dir}")
            return []

        print(f"📁 Auto-detected directories:")
        print(f"  Test Case Generator: {test_case_generator_base_dir}")
        print(f"  Business Flows: {business_flows_dir}")
        print(f"  Output: {output_dir}")

        return GenTestCaseBussinessFlow.process_all_business_flows(
            test_case_generator_base_dir=test_case_generator_base_dir,
            business_flows_dir=business_flows_dir,
            base_output_dir=output_dir,
            config_file=config_file,
            count_token=count_token
        )

    def get_next_test_case_id(self) -> str:
        """
        Generate next unique test case ID in format TC-001, TC-002, etc.
        Thread-safe implementation.

        Returns:
            str: Unique test case ID
        """
        with self.counter_lock:
            self.test_case_counter += 1
            # Include business flow context in ID if available
            if self.business_flow_number:
                return f"TC-BF{self.business_flow_number:02d}-{self.test_case_counter:03d}"
            else:
                return f"TC-{self.test_case_counter:03d}"

    def reset_test_case_counter(self):
        """
        Reset the test case counter. Useful when starting a new business flow.
        """
        with self.counter_lock:
            self.test_case_counter = 0

    def _rotate_and_reconfigure_api(self):
        """Rotates the API key, reconfigures generativeai, and re-initializes the model."""
        print(f"Quota exceeded with key ending ...{self.api_key[-4:]}. Rotating API key...")
        try:
            self.rotator.rotate_api_key()
            self.api_key = self.rotator.get_api_key()
            self._configure_client_and_cache()
            print(f"Switched to new API key ending: ...{self.api_key[-4:]}")
            return True # Indicate success
        except Exception as e:
            print(f"Error during API key rotation or reconfiguration: {e}")
            print(traceback.format_exc())
            return False # Indicate failure

    def _attempt_generate_content(self, prompt: str) -> str:
        """Attempts to generate content using the current model, handling retries and key rotation."""
        if not self.initialization_ok or not self.rotator or not self.model:
             return "Error: Generator not properly initialized."

        # Count input tokens if enabled
        input_tokens = 0
        if self.count_token:
            try:
                input_token_count = self.model.count_tokens(prompt)
                input_tokens = input_token_count.total_tokens
                print(f"Input tokens: {input_tokens:,}")
                
                # Thread-safe token counting
                with self.token_lock:
                    self.total_input_tokens += input_tokens
                    self.api_call_count += 1
            except Exception as e:
                print(f"Failed to count input tokens: {e}")

        max_attempts = len(self.rotator.api_keys)
        for attempt in range(max_attempts):
            try:
                print(f"Attempt {attempt + 1}/{max_attempts} using key ending ...{self.api_key[-4:]}")

                # --- START: Modified API Call with Cache ---
                if self.relevant_info_cache:
                    print(f"Using cache: {self.relevant_info_cache.display_name} ({self.relevant_info_cache.name})")
                    response = self.client.models.generate_content(
                        model=self.model_name,
                        contents=prompt,
                        config=types.GenerateContentConfig(
                            cached_content=self.relevant_info_cache.name
                        )
                    )
                else:
                    response = self.model.generate_content(prompt)
                # --- END: Modified API Call with Cache ---

                # Count output tokens if enabled
                if self.count_token and response:
                    output_tokens = 0
                    try:
                        if hasattr(response, 'usage_metadata') and response.usage_metadata:
                            output_tokens = response.usage_metadata.candidates_token_count
                        else:
                            # Fallback: estimate tokens based on response text
                            if hasattr(response, "text"):
                                output_token_count = self.model.count_tokens(response.text)
                                output_tokens = output_token_count.total_tokens
                        
                        # Thread-safe token counting
                        with self.token_lock:
                            self.total_output_tokens += output_tokens
                        
                        # Calculate and log cost for this request
                        cost_info = self._calculate_token_cost(input_tokens, output_tokens)
                        if cost_info:
                            print(f"Request cost: ${cost_info.get('total_cost_usd', 0):.6f}")
                    except Exception as e:
                        print(f"Failed to count output tokens: {e}")

                if response and hasattr(response, "text") and response.text:
                    return response.text
                elif response and hasattr(response, "prompt_feedback"):
                     print(f"Warning: Received response with feedback: {response.prompt_feedback}")
                     return f"Error: Generation failed due to API feedback - {response.prompt_feedback}"
                else:
                     print(f"Warning: Received unexpected response format or no text: {response}")
                     return "Error: No valid response text generated."

            except ResourceExhausted as e:
                print(f"ResourceExhausted error on attempt {attempt + 1}.")
                if attempt < max_attempts - 1:
                    if not self._rotate_and_reconfigure_api():
                         return f"Error: Failed to rotate API key - {e}"
                else:
                    print(f"Error: Exhausted all {max_attempts} API keys.")
                    return f"Error: All API keys exhausted - {e}"

            except Exception as e:
                print(f"Unexpected error during API call on attempt {attempt + 1}: {e}")
                print(traceback.format_exc())
                return f"Error: An unexpected exception occurred - {e}"

        return "Error: Generation failed after all attempts."

    def _process_single_row(self, row_data, row_index, header, input_data_content):
        """
        Processes a single row of data and generates test case for it.
        This method will be called by multiple threads.
        """
        current_row_number = row_index + 1
        thread_start_time = time.time()
        
        print(f"[Thread {current_row_number}] Starting test case generation for row {current_row_number}")

        try:
            if len(row_data) != len(header):
                error_msg = f"Column count mismatch for row {current_row_number}"
                print(f"[Thread {current_row_number}] Error: {error_msg}")
                with self.results_lock:
                    self.results[row_index] = f"Error: {error_msg}"
                return

            prompt_input_parts = []
            row_details_for_logging = {}

            for col_index, cell_value in enumerate(row_data):
                col_name = header[col_index]
                clean_cell = cell_value.strip()
                processed_value_for_prompt = f'"{clean_cell}"'

                try:
                    if (clean_cell.startswith('{') and clean_cell.endswith('}')) or \
                       (clean_cell.startswith('[') and clean_cell.endswith(']')):
                        parsed_json = json.loads(clean_cell)
                        processed_value_for_prompt = json.dumps(clean_cell)
                        row_details_for_logging[col_name] = parsed_json
                    else:
                        row_details_for_logging[col_name] = clean_cell
                except json.JSONDecodeError:
                    row_details_for_logging[col_name] = clean_cell

                prompt_input_parts.append(f'  - Column "{col_name}": {processed_value_for_prompt}')

            full_input_prompt_str = "\n".join(prompt_input_parts)

            # Generate unique test case ID
            unique_test_case_id = self.get_next_test_case_id()

            # Include use case information if available
            use_case_context = ""
            if self.use_case:
                try:
                    use_case_context = f"*Use Case Context:*\n{json.dumps(self.use_case, indent=2)}\n"
                except Exception as e:
                    print(f"[Thread {current_row_number}] Warning: Could not format use case context: {e}")

            # Create modified prompt with unique test case ID
            modified_prompt = prompt_storing.gen_tc_pairwise_prompt.replace(
                '"TEST CASE ID": "TC-001"',
                f'"TEST CASE ID": "{unique_test_case_id}"'
            )

            prompt = (
                f"{modified_prompt}\n"
                f"*Feature Context:* Test cases related to '{self.base_filename}'\n"
                f"{use_case_context}"  # Include use case context if available
                f"*Input Data for this Specific Case (Row {current_row_number}):*\n"
                f"{full_input_prompt_str}\n"
                f"*Important:* Use the TEST CASE ID '{unique_test_case_id}' in your response.\n"
            )
    
            result_text = self._attempt_generate_content(prompt)
            
            # Thread-safe storage of results
            with self.results_lock:
                self.results[row_index] = result_text
            
            thread_elapsed_time = time.time() - thread_start_time
            print(f"[Thread {current_row_number}] Completed ({thread_elapsed_time:.2f}s). Test case for row {current_row_number} generated.")

        except Exception as e:
            error_msg = f"Error processing row {current_row_number}: {e}"
            print(f"[Thread {current_row_number}] {error_msg}")
            print(traceback.format_exc())
            with self.results_lock:
                self.results[row_index] = f"Error: {error_msg}"

    # --- MODIFIED generate_test_case Method with Multithreading ---
    def generate_test_case(self):
        """
        Generates test cases using the Gemini model with multithreading support,
        processing multiple CSV data rows concurrently.
        """
        if not self.initialization_ok:
            print("Cannot generate test cases: Initialization failed.")
            return []

        try:
            parsed_rows = []
            header = []
            with open(self.csv_file, 'r', encoding='utf-8', newline='') as f:
                reader = csv.reader(f)
                try:
                    header = next(reader)
                    header = [col.strip() for col in header]
                except StopIteration:
                    print(f"Warning: CSV file '{self.csv_file}' seems to be empty.")
                    return []

                for row in reader:
                    if row and any(cell.strip() for cell in row):
                        parsed_rows.append(row)

            if not header or not parsed_rows:
                print(f"Error: Could not read header or data from CSV file '{self.csv_file}'.")
                return []

            # Read supplementary input data if available
            input_data_content = ""
            if self.input_data:
                try:
                    with open(self.input_data, 'r', encoding='utf-8') as f:
                        input_data_content = f.read()
                except Exception as e:
                    print(f"Warning: Could not read input data file: {e}")
                    print("Continuing without input data...")
            else:
                print("No input data file provided. Continuing without supplementary data...")

        except Exception as e:
            print(f"Error reading input files: {e}")
            print(traceback.format_exc())
            return []

        # Reset results for new generation
        self.results = {}
        
        column_names_str = ", ".join(f'"{h}"' for h in header)
        print(f"Using Header ({len(header)} columns): {column_names_str}")
        print(f"Starting multithreaded test case generation for {len(parsed_rows)} rows...\n")

        overall_start_time = time.time()
        threads = []

        # Create and start threads for each row
        for i, row_data in enumerate(parsed_rows):
            thread = threading.Thread(
                target=self._process_single_row,
                args=(row_data, i, header, input_data_content)
            )
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        overall_elapsed_time = time.time() - overall_start_time
        print(f"\n--- All test case generation threads completed in {overall_elapsed_time:.2f} seconds ---")

        # Collect results in the original order
        all_test_cases = []
        for i in range(len(parsed_rows)):
            if i in self.results:
                all_test_cases.append(self.results[i])
            else:
                all_test_cases.append("Error: No result generated for this row")

        print(f"--- Test case generation finished with {len(all_test_cases)} results ---")
        return all_test_cases

    # --- execute, save_test_cases ---
    def execute(self):
        """Executes the test case generation and saves the result."""
        # Add business flow context to logging
        bf_context = f" (Business Flow {self.business_flow_number})" if self.business_flow_number else ""
        print(f"🔄 Starting test case generation{bf_context}...")

        # Reset counter at the beginning of each execution to ensure consistent numbering
        # Only reset if this is a new CSV file (not continuing from previous)
        if hasattr(self, '_last_processed_file') and self._last_processed_file != self.csv_file:
            self.reset_test_case_counter()
        elif not hasattr(self, '_last_processed_file'):
            self.reset_test_case_counter()

        self._last_processed_file = self.csv_file

        if not self.initialization_ok:
             print(f"❌ Execution skipped{bf_context}: Initialization was not successful.")
             return []

        all_test_case_responses = self.generate_test_case()

        gemini_test_cases = []
        successful_generations = 0
        failed_parsing = 0
        api_errors = 0

        for i, response_text in enumerate(all_test_case_responses):
            if isinstance(response_text, str) and response_text.startswith("Error:"):
                print(f"API Error for item {i+1}: {response_text}")
                gemini_test_cases.append({"error": response_text})
                api_errors += 1
            else:
                try:
                    # CRITICAL: Attempt to clean the response before parsing if needed
                    # Basic cleaning: remove potential markdown ```json ... ``` markers
                    cleaned_response_text = response_text.strip()
                    if cleaned_response_text.startswith("```json"):
                        cleaned_response_text = cleaned_response_text[7:]
                    if cleaned_response_text.endswith("```"):
                        cleaned_response_text = cleaned_response_text[:-3]
                    cleaned_response_text = cleaned_response_text.strip()

                    # Now try parsing the cleaned text
                    parsed_json = json.loads(cleaned_response_text)
                    gemini_test_cases.append(parsed_json)
                    successful_generations += 1
                except json.JSONDecodeError as e:
                    print(f"Failed to parse JSON response for item {i+1}: {e}")
                    print(f"Received text (cleaned, first 300 chars): {cleaned_response_text[:300]}...")
                    gemini_test_cases.append({"error": f"Invalid JSON response after cleaning: {e}", "raw_response": response_text})
                    failed_parsing += 1
                except Exception as e:
                    print(f"Unexpected error processing response for item {i+1}: {e}")
                    gemini_test_cases.append({"error": f"Unexpected processing error: {e}", "raw_response": response_text})
                    failed_parsing += 1 # Count as parsing failure

        total_attempted = len(all_test_case_responses)
        bf_context = f" (Business Flow {self.business_flow_number})" if self.business_flow_number else ""

        print(f"\n{'='*50}")
        print(f"TEST CASE GENERATION SUMMARY{bf_context}")
        print(f"{'='*50}")
        print(f"📊 Total items processed: {total_attempted}")
        print(f"✅ Successfully generated and parsed: {successful_generations}")
        print(f"⚠️ Failed JSON parsing: {failed_parsing}")
        print(f"❌ API/Generation errors: {api_errors}")
        print(f"📁 Output directory: {self.output_directory}")

        if gemini_test_cases:
            self.save_test_cases(gemini_test_cases, self.output_directory)
            print(f"🎉 Test case generation completed{bf_context}")
        else:
            print(f"⚠️ No test cases were generated or parsed successfully{bf_context}")

        # Print token summary if enabled
        if self.count_token:
            self.print_token_summary()

        return gemini_test_cases

    def save_test_cases(self, test_cases, directory):
        """Saves the generated test cases as a properly formatted JSON file."""
        # Add business flow context to logging
        bf_context = f" (Business Flow {self.business_flow_number})" if self.business_flow_number else ""

        os.makedirs(directory, exist_ok=True)
        script_filename = os.path.join(directory, f'{self.base_filename}.json')
        try:
            with open(script_filename, 'w', encoding='utf-8') as script_file:
                json.dump(test_cases, script_file, indent=4, ensure_ascii=False)
            print(f"✅ Test cases saved to: {script_filename}{bf_context}")
        except Exception as e:
            print(f"❌ Error saving test cases to {script_filename}{bf_context}: {e}")
            print(traceback.format_exc())
