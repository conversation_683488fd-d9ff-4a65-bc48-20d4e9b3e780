"""
Configuration settings for the application
"""
from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # API Settings
    api_v1_str: str = "/api/v1"
    project_name: str = "Python Interactive Execution Dashboard"
    
    # CORS Settings
    backend_cors_origins: List[str] = ["http://localhost:3000", "http://localhost:5173"]
    
    # File System Settings
    workspace_root: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "workspace")
    pipelines_dir: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "pipelines")
    
    # WebSocket Settings
    websocket_heartbeat_interval: int = 30
    
    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()
