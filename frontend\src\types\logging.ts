/**
 * Logging Data Contract - TypeScript interfaces for WebSocket communication
 */

export type LogLevel = 'INFO' | 'WARN' | 'ERROR' | 'DEBUG' | 'SUCCESS';

export interface LogMessage {
  type: 'log';
  timestamp: string; // ISO 8601 format
  level: LogLevel;
  message: string;
  step_id: string; // e.g., 'document_processor'
  run_id: string;
  metadata?: Record<string, any>; // Optional field for extra data
}

export interface StepUpdateMessage {
  type: 'step_update';
  timestamp: string;
  step_id: string;
  status: 'running' | 'completed' | 'failed' | 'pending';
  progress?: number; // 0-100
  run_id: string;
}

export interface ConnectionMessage {
  type: 'connection_established';
  client_id: string;
  run_id: string;
  timestamp: string;
}

export interface ErrorMessage {
  type: 'error';
  message: string;
  timestamp: string;
}

export interface PingMessage {
  type: 'ping';
  timestamp: string;
}

export interface PongMessage {
  type: 'pong';
  timestamp: string;
}

export type WebSocketMessage = 
  | LogMessage 
  | StepUpdateMessage 
  | ConnectionMessage 
  | ErrorMessage 
  | PingMessage 
  | PongMessage;

export interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  clientId: string | null;
  runId: string | null;
}

export interface LogEntry extends LogMessage {
  id: string; // Unique identifier for React keys
  formattedTime: string; // Human-readable time format
}

// Color mapping for log levels
export const LOG_LEVEL_COLORS: Record<LogLevel, string> = {
  INFO: 'text-blue-400',
  WARN: 'text-yellow-400',
  ERROR: 'text-red-400',
  DEBUG: 'text-gray-400',
  SUCCESS: 'text-green-400'
};

// Background colors for log levels
export const LOG_LEVEL_BG_COLORS: Record<LogLevel, string> = {
  INFO: 'bg-blue-500/10',
  WARN: 'bg-yellow-500/10',
  ERROR: 'bg-red-500/10',
  DEBUG: 'bg-gray-500/10',
  SUCCESS: 'bg-green-500/10'
};

// Icons for log levels
export const LOG_LEVEL_ICONS: Record<LogLevel, string> = {
  INFO: 'ℹ️',
  WARN: '⚠️',
  ERROR: '❌',
  DEBUG: '🔍',
  SUCCESS: '✅'
};
