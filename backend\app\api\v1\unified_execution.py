"""
PHOENIX PROJECT: Unified Execution Endpoints
The SINGLE SOURCE OF TRUTH for pipeline execution.
Rebuilt from the ground up with absolute precision.
"""
import os
import uuid
import json
import re

from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from pathlib import Path

from ...core.log_emitter import get_logger, set_logger_run_id, LogEmitter
from ...core.pipeline_registry import execute_pipeline_step

router = APIRouter()

# Get the logger instance once when the module loads
logger = get_logger()

# PHOENIX BLUEPRINT: Single Source of Truth for File Registry
file_processing_registry: Dict[str, Dict[str, Any]] = {}

# PHOENIX BLUEPRINT: Expected output files for each pipeline step
EXPECTED_OUTPUT_FILES = {
    "document_processor": ["merged_output.json", "document_structure.json"],
    "business_flow_detector": ["business_flows/", "business_flow_summary.json"],
    "relevant_content_processor": ["screen_graph.json"],  # Also creates business_flow_X/ directories
    "path_finder": ["screen_flow_graph.png"],  # Also creates business_flow_X/ directories with screen_paths.json
    "path_to_csv": [],  # Creates business_flow_X/ directories with test_paths.csv files
    "test_case_generator": [],  # Creates business_flow_X/ directories with test cases
    "test_case_evaluator": []  # Creates business_flow_X/ directories with evaluation results
}

async def validate_step_output(step_id: str, output_dir: str, logger: LogEmitter, run_id: str) -> bool:
    """
    Validate that a pipeline step actually produced meaningful output.

    Args:
        step_id: The pipeline step identifier
        output_dir: Base output directory
        logger: Logger instance
        run_id: Run identifier for logging

    Returns:
        bool: True if output validation passed, False otherwise
    """
    expected_files = EXPECTED_OUTPUT_FILES.get(step_id, [])

    if not expected_files:
        # For steps without explicit expected files, check if step output directory exists and has content
        step_output_dir = os.path.join(output_dir, step_id)
        if not os.path.exists(step_output_dir):
            await logger.log(run_id, "ERROR", f"❌ Step output directory missing: {step_id}", step_id)
            return False

        # Check if directory has any content (files or subdirectories)
        try:
            contents = os.listdir(step_output_dir)
            if not contents:
                await logger.log(run_id, "ERROR", f"❌ Step output directory is empty: {step_id}", step_id)
                return False
            else:
                return True
        except Exception as e:
            await logger.log(run_id, "ERROR", f"❌ Error checking step output directory {step_id}: {e}", step_id)
            return False

    step_output_dir = os.path.join(output_dir, step_id)
    validation_passed = True

    for expected_file in expected_files:
        if expected_file.endswith('/'):  # Directory check
            # Check both in step output dir and base output dir (for business_flows/)
            expected_path = os.path.join(step_output_dir, expected_file.rstrip('/'))
            if not os.path.exists(expected_path):
                # Try base output dir for shared directories like business_flows/
                expected_path = os.path.join(output_dir, expected_file.rstrip('/'))

            if not os.path.exists(expected_path) or not os.path.isdir(expected_path):
                await logger.log(run_id, "ERROR", f"❌ Expected output directory missing: {expected_file}", step_id)
                validation_passed = False
            else:
                # Check if directory has any files
                try:
                    files_in_dir = os.listdir(expected_path)
                    if not files_in_dir:
                        await logger.log(run_id, "ERROR", f"❌ Expected output directory is empty: {expected_file}", step_id)
                        validation_passed = False
                    else:
                        pass
                except Exception as e:
                    await logger.log(run_id, "ERROR", f"❌ Error checking directory {expected_file}: {e}", step_id)
                    validation_passed = False
        else:  # File check
            expected_path = os.path.join(step_output_dir, expected_file)
            if not os.path.exists(expected_path):
                await logger.log(run_id, "ERROR", f"❌ Expected output file missing: {expected_file}", step_id)
                validation_passed = False
            else:
                # Check if file has content
                try:
                    file_size = os.path.getsize(expected_path)
                    if file_size == 0:
                        await logger.log(run_id, "ERROR", f"❌ Expected output file is empty: {expected_file}", step_id)
                        validation_passed = False
                    else:
                        pass
                except Exception as e:
                    await logger.log(run_id, "ERROR", f"❌ Error checking file {expected_file}: {e}", step_id)
                    validation_passed = False

    if validation_passed:
        pass
    else:
        await logger.log(run_id, "ERROR", f"❌ Output validation failed for {step_id}", step_id)

    return validation_passed

# PHOENIX BLUEPRINT: Request/Response Models
class ExecuteStepRequest(BaseModel):
    stepId: str
    fileId: str
    runId: str
    parameters: Dict[str, Any] = {}

class UploadResponse(BaseModel):
    fileId: str
    fileName: str
    status: str
    message: str

# PHOENIX BLUEPRINT: Convention over Configuration - Context Factory
def build_execution_context(run_id: str, file_name: str, input_file_path: str) -> dict:
    """
    Build comprehensive execution context based on directory structure convention.
    This eliminates all None path errors by pre-defining all paths.

    Args:
        run_id: Unique execution identifier
        file_name: Original filename (will be sanitized)
        input_file_path: Path to the uploaded PDF file

    Returns:
        Complete execution context dictionary with all paths pre-defined
    """
    # Sanitize filename for directory structure
    base_name = Path(file_name).stem
    sanitized_name = re.sub(r'[^a-zA-Z0-9\-_]', '_', base_name)
    sanitized_name = re.sub(r'_+', '_', sanitized_name).strip('_')

    # Base output directory (without run_id as per new structure)
    base_output_dir = os.path.join("output", sanitized_name)

    # Pre-define all step output directories based on convention
    step_outputs = {
        "document_processor": os.path.join(base_output_dir, "document_processor"),
        "business_flow_detector": os.path.join(base_output_dir, "business_flow_detector"),
        "relevant_content_processor": os.path.join(base_output_dir, "relevant_content_processor"),
        "path_finder": os.path.join(base_output_dir, "path_finder"),
        "path_to_csv": os.path.join(base_output_dir, "path_to_csv"),
        "test_case_generator": os.path.join(base_output_dir, "test_case_generator"),
        "test_case_evaluator": os.path.join(base_output_dir, "test_case_evaluator")
    }

    # Create all directories upfront to prevent None path errors
    os.makedirs(base_output_dir, exist_ok=True)
    for step_dir in step_outputs.values():
        os.makedirs(step_dir, exist_ok=True)

    # Create key subdirectories
    business_flows_dir = os.path.join(step_outputs["business_flow_detector"], "business_flows")
    relevant_content_dir = os.path.join(step_outputs["relevant_content_processor"], "relevant_content")
    os.makedirs(business_flows_dir, exist_ok=True)
    os.makedirs(relevant_content_dir, exist_ok=True)

    # Build complete execution context
    context = {
        "run_id": run_id,
        "base_output_dir": base_output_dir,
        "input_file_path": input_file_path,
        "sanitized_file_name": sanitized_name,
        "step_outputs": step_outputs,
        # CORRECTED: Pre-define key file paths based on REAL branching architecture
        "key_files": {
            # Document processor outputs (single files)
            "merged_output": os.path.join(step_outputs["document_processor"], "merged_output.json"),
            "description_output": os.path.join(step_outputs["document_processor"], "description_output.json"),

            # Business flows directory (contains individual flow files)
            "business_flows_dir": os.path.join(base_output_dir, "business_flows"),

            # BRANCHING ARCHITECTURE: Each step creates subdirectories per business flow
            "relevant_content_base": step_outputs["relevant_content_processor"],
            "screen_graph": os.path.join(step_outputs["relevant_content_processor"], "screen_graph.json"),

            "path_processor_base": step_outputs["path_finder"],
            "screen_flow_graph": os.path.join(step_outputs["path_finder"], "screen_flow_graph.png"),

            "test_case_csv_base": step_outputs["path_to_csv"],
            "test_case_output_base": os.path.join(base_output_dir, "TestCase_Output"),

            "final_test_cases_base": step_outputs["test_case_generator"],
            "evaluation_base": step_outputs["test_case_evaluator"]
        },

        # BRANCHING METADATA: Functions to generate per-flow paths
        "flow_path_generators": {
            "relevant_content_flow": lambda flow_id: os.path.join(step_outputs["relevant_content_processor"], f"business_flow_{flow_id}"),
            "path_processor_flow": lambda flow_id: os.path.join(step_outputs["path_finder"], f"business_flow_{flow_id}"),
            "test_csv_flow": lambda flow_id: os.path.join(step_outputs["path_to_csv"], f"business_flow_{flow_id}"),
            "test_output_flow": lambda flow_id: os.path.join(base_output_dir, "TestCase_Output", f"business_flow_{flow_id}"),
            "final_test_flow": lambda flow_id: os.path.join(step_outputs["test_case_generator"], f"business_flow_{flow_id}"),
            "evaluation_flow": lambda flow_id: os.path.join(step_outputs["test_case_evaluator"], f"business_flow_{flow_id}")
        }
    }

    return context

def create_session_output_directory(filename: str, run_id: str = None) -> str:
    """Legacy function - kept for backward compatibility"""
    base_name = Path(filename).stem
    sanitized_name = re.sub(r'[^a-zA-Z0-9\-_]', '_', base_name)
    sanitized_name = re.sub(r'_+', '_', sanitized_name).strip('_')

    session_dir = os.path.join("output", sanitized_name)
    os.makedirs(session_dir, exist_ok=True)
    return session_dir

# PHOENIX BLUEPRINT: Context-Based Background Execution Function
async def execute_step_background_with_context(
    step_id: str,
    execution_context: Dict[str, Any],
    parameters: Dict[str, Any]
):
    """
    Execute pipeline step with complete execution context.
    This eliminates None path errors by providing all paths upfront.
    """
    run_id = execution_context["run_id"]

    # Get logger
    logger = get_logger()

    try:

        # Send step_update WebSocket message for start
        try:
            from ...core.websocket_manager import connection_manager
            await connection_manager.send_step_update(run_id, step_id, "running")
        except Exception as e:
            pass

        # DEFENSIVE: Validate context has all required paths
        if not execution_context.get("step_outputs", {}).get(step_id):
            raise ValueError(f"Context missing step_outputs for {step_id}")

        # DEFENSIVE: Validate all paths in context are not None
        for key, path in execution_context["step_outputs"].items():
            if path is None:
                raise ValueError(f"Context has None path for step: {key}")

        # Import and execute the pipeline step with context
        from ...core.pipeline_registry import PIPELINE_REGISTRY

        if step_id not in PIPELINE_REGISTRY:
            raise ValueError(f"Pipeline step '{step_id}' not found in registry")

        pipeline_function = PIPELINE_REGISTRY[step_id]

        # ENHANCED: Add context-aware parameters for legacy compatibility
        enhanced_parameters = {
            **parameters,
            "run_id": run_id,
            "step_id": step_id,
            "output_dir": execution_context["base_output_dir"],
            # Add step-specific legacy parameters
            "input_path": execution_context["input_file_path"],
        }

        # Add step-specific legacy parameter mappings
        if step_id == "relevant_content_processor":
            enhanced_parameters.update({
                "business_flows_path": execution_context["key_files"]["business_flows_dir"],
                "document_structure_path": execution_context["key_files"]["merged_output"],
            })

        # Execute with context-based parameters (maintaining backward compatibility)
        result = await pipeline_function(
            params=enhanced_parameters,  # Enhanced parameters with context
            logger=logger,
            context=execution_context,  # New context parameter
            **enhanced_parameters  # Additional kwargs for flexibility
        )

        # Send step_update WebSocket message for success
        try:
            from ...core.websocket_manager import connection_manager
            await connection_manager.send_step_update(run_id, step_id, "completed")
            await logger.log(run_id, "INFO", f"✅ Step completed successfully: {step_id}", step_id)
        except Exception as e:
            pass

        return result

    except Exception as e:
        # Send step_update WebSocket message for failure
        try:
            from ...core.websocket_manager import connection_manager
            await connection_manager.send_step_update(run_id, step_id, "failed")
        except Exception:
            pass

        await logger.log(run_id, "ERROR", f"❌ Step failed: {step_id} - {str(e)}", step_id)
        await logger.log(run_id, "ERROR", f"🔍 Error details: {type(e).__name__}: {str(e)}", step_id)
        raise

# LEGACY: Keep old function for backward compatibility
async def execute_step_background(
    step_id: str,
    file_id: str,
    run_id: str,
    input_path: str,
    output_dir: str,
    parameters: Dict[str, Any]
):
    """
    PHOENIX PROJECT: The SINGLE background execution function.
    This is where ALL pipeline steps are executed.
    """
    try:
        # Set logger context
        set_logger_run_id(run_id)

        # PART 1: PRE-FLIGHT DEPENDENCY CHECK - FAIL FAST ARCHITECTURE

        # Import dependency configurations
        from app.core.pipeline_registry import PIPELINE_DEPENDENCIES, REQUIRED_INPUT_FILES

        # Check 1: Validate dependencies completed successfully
        dependencies = PIPELINE_DEPENDENCIES.get(step_id, [])
        if dependencies:
            pass

            for dep_step in dependencies:
                # STATE MANAGEMENT: Check for _SUCCESS file to verify dependency completion
                dep_output_dir = os.path.join(output_dir, dep_step)
                success_file = os.path.join(dep_output_dir, "_SUCCESS")

                if not os.path.exists(success_file):
                    # Check if directory exists to provide better error message
                    if not os.path.exists(dep_output_dir):
                        error_msg = f"❌ Cannot run {step_id} because dependency {dep_step} has not been executed (directory missing)"
                    else:
                        error_msg = f"❌ Cannot run {step_id} because dependency {dep_step} did not complete successfully (no _SUCCESS marker)"

                    await logger.log(run_id, "ERROR", error_msg, step_id)
                    return {"status": "error", "message": error_msg}

        # Check 2: Validate required input files exist
        required_files = REQUIRED_INPUT_FILES.get(step_id, [])
        if required_files:
            pass

            for required_file in required_files:
                if required_file.endswith('/'):  # Directory check
                    required_path = os.path.join(output_dir, required_file.rstrip('/'))
                    if not os.path.exists(required_path) or not os.path.isdir(required_path):
                        error_msg = f"❌ Cannot run {step_id} because required directory {required_file} is missing"
                        await logger.log(run_id, "ERROR", error_msg, step_id)
                        return {"status": "error", "message": error_msg}
                else:  # File check
                    # Look for the file in any dependency step directory
                    file_found = False
                    for dep_step in dependencies:
                        potential_path = os.path.join(output_dir, dep_step, required_file)
                        if os.path.exists(potential_path):
                            file_found = True
                            break

                    if not file_found:
                        error_msg = f"❌ Cannot run {step_id} because required input file {required_file} is missing"
                        await logger.log(run_id, "ERROR", error_msg, step_id)
                        return {"status": "error", "message": error_msg}

        # PART 2: EXECUTE THE PIPELINE STEP

        # Execute the pipeline step using the registry
        result = await execute_pipeline_step(
            step_id=step_id,
            input_path=input_path,
            output_dir=output_dir,
            logger=logger,
            run_id=run_id,
            **parameters
        )
        
        if result and result.get("status") == "success":
            # ENHANCED: Validate that step actually produced meaningful output
            output_validation_passed = await validate_step_output(step_id, output_dir, logger, run_id)

            if output_validation_passed:
                pass

                # Send step_update WebSocket message for frontend status update
                try:
                    from ...core.websocket_manager import connection_manager
                    await connection_manager.send_step_update(run_id, step_id, "success")
                except Exception as e:
                    pass

                # STATE MANAGEMENT: Create _SUCCESS file to mark step completion
                try:
                    step_output_dir = os.path.join(output_dir, step_id)
                    os.makedirs(step_output_dir, exist_ok=True)
                    success_file = os.path.join(step_output_dir, "_SUCCESS")

                    with open(success_file, 'w') as f:
                        f.write(f"Step {step_id} completed successfully at {datetime.now().isoformat()}\n")
                        f.write(f"Run ID: {run_id}\n")
                        f.write("Status: success\n")
                except Exception as e:
                    pass
            else:
                # Override success status if output validation failed
                result["status"] = "error"
                result["message"] = f"Step {step_id} completed but did not produce valid output"
                await logger.log(run_id, "ERROR", f"❌ {step_id} failed output validation", step_id)

                # Send step_update WebSocket message for failure
                try:
                    from ...core.websocket_manager import connection_manager
                    await connection_manager.send_step_update(run_id, step_id, "error")
                except Exception as e:
                    pass

        # Send token/cost metadata if available (regardless of validation result)
        if result and result.get("metadata"):
            metadata = result["metadata"]
            if "tokens" in metadata or "cost" in metadata:
                await logger.log(
                    run_id, "INFO",
                    f"📊 Tokens: {metadata.get('tokens', 0)}, Cost: ${metadata.get('cost', 0):.4f}",
                    step_id,
                    metadata={"tokens": metadata.get("tokens", 0), "cost": metadata.get("cost", 0)}
                )

        # Handle failure cases (both original failures and output validation failures)
        if not result or result.get("status") != "success":
            error_msg = result.get("message", "Unknown error") if result else "No result returned"
            await logger.log(run_id, "ERROR", f"❌ {step_id} failed: {error_msg}", step_id)

            # Send step_update WebSocket message for failure (if not already sent)
            if not (result and result.get("status") == "error" and "output validation failed" in result.get("message", "")):
                try:
                    from ...core.websocket_manager import connection_manager
                    await connection_manager.send_step_update(run_id, step_id, "error")
                except Exception as e:
                    pass

    except Exception as e:
        await logger.log(run_id, "ERROR", f"❌ {step_id} crashed: {str(e)}", step_id)

        # Send step_update WebSocket message for crash
        try:
            from ...core.websocket_manager import connection_manager
            await connection_manager.send_step_update(run_id, step_id, "error")
        except Exception as ex:
            pass

# PHOENIX BLUEPRINT: API Endpoints

@router.post("/upload-and-prepare", response_model=UploadResponse)
async def upload_and_prepare(file: UploadFile = File(...)):
    """
    PHOENIX PROJECT: Upload file and prepare for execution.
    Returns fileId and fileName for the frontend to store in global state.
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="Only PDF files are supported")
        
        # Generate unique file ID
        file_id = f"file_{uuid.uuid4().hex[:12]}"
        
        # Create uploads directory
        uploads_dir = "uploads"
        os.makedirs(uploads_dir, exist_ok=True)
        
        # Save file with unique name
        file_extension = Path(file.filename).suffix
        unique_filename = f"{file_id}{file_extension}"
        file_path = os.path.join(uploads_dir, unique_filename)
        
        # Save the uploaded file
        content = await file.read()
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Store file metadata in registry
        file_processing_registry[file_id] = {
            "file_id": file_id,
            "filename": file.filename,
            "file_path": file_path,
            "upload_time": str(uuid.uuid4()),  # Simple timestamp
            "status": "ready"
        }
        
        return UploadResponse(
            fileId=file_id,
            fileName=file.filename,
            status="success",
            message=f"File '{file.filename}' uploaded successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.post("/execute-step", status_code=202)
async def execute_pipeline_step_endpoint(request: ExecuteStepRequest, background_tasks: BackgroundTasks):
    """
    PHOENIX PROJECT: Execute a pipeline step.
    THE SINGLE SOURCE OF TRUTH for pipeline execution.
    Returns 202 Accepted immediately and processes in background.
    """
    try:
        # Validate file exists
        if request.fileId not in file_processing_registry:
            await logger.log(request.runId, "ERROR", f"❌ File not found: {request.fileId}", request.stepId)
            raise HTTPException(status_code=404, detail="File not found")

        # Get file info
        file_info = file_processing_registry[request.fileId]

        # CONVENTION OVER CONFIGURATION: Build complete execution context
        execution_context = build_execution_context(
            run_id=request.runId,
            file_name=file_info["filename"],
            input_file_path=file_info["file_path"]
        )

        # Schedule background task with complete execution context
        background_tasks.add_task(
            execute_step_background_with_context,
            step_id=request.stepId,
            execution_context=execution_context,
            parameters=request.parameters
        )

        return {
            "status": "accepted",
            "message": f"Pipeline step '{request.stepId}' started",
            "runId": request.runId,
            "sessionOutputDir": execution_context['base_output_dir']
        }

    except HTTPException:
        raise
    except Exception as e:
        await logger.log(request.runId, "ERROR", f"❌ Failed to start execution: {str(e)}", request.stepId)
        raise HTTPException(status_code=500, detail=f"Failed to start execution: {str(e)}")

@router.get("/artifacts/{run_id}/{step_id}")
async def get_artifacts(run_id: str, step_id: str):
    """
    PHOENIX PROJECT: Get artifacts with BRANCHING ARCHITECTURE support.
    Understands that some steps create per-business-flow subdirectories.
    """
    try:
        # Find the session directory (now without run_id in path)
        output_base = "output"
        if not os.path.exists(output_base):
            raise HTTPException(status_code=404, detail="No output directory found")

        # Look for any filename directory that contains the step
        step_dirs = []
        for item in os.listdir(output_base):
            item_path = os.path.join(output_base, item)
            if os.path.isdir(item_path):
                step_path = os.path.join(item_path, step_id)
                if os.path.exists(step_path):
                    step_dirs.append(step_path)

        if not step_dirs:
            raise HTTPException(status_code=404, detail=f"Step directory not found: {step_id}")

        # Use the first matching step directory
        step_dir = step_dirs[0]

        # BRANCHING ARCHITECTURE: Detect if this step has per-flow structure
        branching_steps = ["relevant_content_processor", "path_processor", "test_case_csv_generator", "TestCase_Output"]
        is_branching_step = step_id in branching_steps

        artifacts = []

        if is_branching_step:
            # BRANCHING STEP: Organize by business flows
            business_flows = {}

            for root, dirs, files in os.walk(step_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, step_dir)

                    # Determine which business flow this belongs to
                    path_parts = relative_path.split(os.sep)
                    if len(path_parts) > 1 and path_parts[0].startswith("business_flow_"):
                        flow_id = path_parts[0]
                        if flow_id not in business_flows:
                            business_flows[flow_id] = []

                        # Read file content
                        try:
                            if file.endswith(('.json', '.txt', '.csv')):
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                business_flows[flow_id].append({
                                    "name": os.sep.join(path_parts[1:]) if len(path_parts) > 1 else file,
                                    "type": "text",
                                    "content": content,
                                    "flow_id": flow_id
                                })
                            else:
                                business_flows[flow_id].append({
                                    "name": os.sep.join(path_parts[1:]) if len(path_parts) > 1 else file,
                                    "type": "binary",
                                    "content": f"Binary file: {file}",
                                    "flow_id": flow_id
                                })
                        except Exception as e:
                            business_flows[flow_id].append({
                                "name": os.sep.join(path_parts[1:]) if len(path_parts) > 1 else file,
                                "type": "error",
                                "content": f"Error reading file: {str(e)}",
                                "flow_id": flow_id
                            })
                    else:
                        # Root level file (like screen_graph.json)
                        try:
                            if file.endswith(('.json', '.txt', '.csv')):
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                artifacts.append({
                                    "name": relative_path,
                                    "type": "text",
                                    "content": content,
                                    "flow_id": "root"
                                })
                            else:
                                artifacts.append({
                                    "name": relative_path,
                                    "type": "binary",
                                    "content": f"Binary file: {file}",
                                    "flow_id": "root"
                                })
                        except Exception as e:
                            artifacts.append({
                                "name": relative_path,
                                "type": "error",
                                "content": f"Error reading file: {str(e)}",
                                "flow_id": "root"
                            })

            # Add business flow artifacts to main artifacts list
            for flow_id, flow_artifacts in business_flows.items():
                artifacts.extend(flow_artifacts)

            return {
                "runId": run_id,
                "stepId": step_id,
                "artifacts": artifacts,
                "count": len(artifacts),
                "structure": "branching",
                "business_flows": list(business_flows.keys()),
                "flow_count": len(business_flows)
            }

        else:
            # LINEAR STEP: Simple file collection
            for root, dirs, files in os.walk(step_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, step_dir)

                    try:
                        if file.endswith(('.json', '.txt', '.csv')):
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            artifacts.append({
                                "name": relative_path,
                                "type": "text",
                                "content": content
                            })
                        else:
                            artifacts.append({
                                "name": relative_path,
                                "type": "binary",
                                "content": f"Binary file: {file}"
                            })
                    except Exception as e:
                        artifacts.append({
                            "name": relative_path,
                            "type": "error",
                            "content": f"Error reading file: {str(e)}"
                        })

            return {
                "runId": run_id,
                "stepId": step_id,
                "artifacts": artifacts,
                "count": len(artifacts),
                "structure": "linear"
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get artifacts: {str(e)}")

@router.get("/files")
async def list_files():
    """List all uploaded files"""
    return {
        "files": [
            {
                "fileId": file_id,
                "fileName": info["filename"],
                "status": info["status"]
            }
            for file_id, info in file_processing_registry.items()
        ]
    }

