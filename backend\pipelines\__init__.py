# Pipeline modules initialization
# This file ensures all pipeline modules are imported and registered

# Import the real pipeline modules that use the user's existing classes
from . import real_pipelines

__all__ = [
    'document_processor',
    'diagram_processor',
    'business_flow_generator',
    'relevant_content_processor',
    'screen_path_generator',
    'test_case_generator',
    'final_test_case_generator',
    'test_case_evaluator'
]
