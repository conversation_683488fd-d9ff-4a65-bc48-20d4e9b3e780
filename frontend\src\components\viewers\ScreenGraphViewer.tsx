import React, { useEffect, useState, useCallback } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  NodeTypes,
} from 'reactflow'
import 'reactflow/dist/style.css'
import { Monitor, Smartphone, Tablet } from 'lucide-react'

interface ScreenGraphViewerProps {
  data: any
}

interface ScreenNodeData {
  label: string
  type: string
  id: string
}

const ScreenNode: React.FC<{ data: ScreenNodeData }> = ({ data }) => {
  const getIcon = () => {
    switch (data.type) {
      case 'mobile':
        return <Smartphone className="w-4 h-4" />
      case 'tablet':
        return <Tablet className="w-4 h-4" />
      default:
        return <Monitor className="w-4 h-4" />
    }
  }

  const getTypeColor = () => {
    switch (data.type) {
      case 'authentication':
        return 'bg-blue-600'
      case 'main':
        return 'bg-green-600'
      case 'transaction':
        return 'bg-orange-600'
      case 'admin':
        return 'bg-red-600'
      case 'catalog':
        return 'bg-purple-600'
      default:
        return 'bg-gray-600'
    }
  }

  return (
    <div className="px-4 py-3 rounded-lg border-2 border-dark-600 bg-dark-800 min-w-[150px] hover:border-primary-500 transition-colors">
      <div className="flex items-center space-x-2 mb-2">
        {getIcon()}
        <span className={`px-2 py-1 rounded text-xs text-white ${getTypeColor()}`}>
          {data.type}
        </span>
      </div>
      <h3 className="font-medium text-white text-sm">{data.label}</h3>
      <p className="text-xs text-dark-300 mt-1">{data.id}</p>
    </div>
  )
}

const nodeTypes: NodeTypes = {
  screen: ScreenNode,
}

export const ScreenGraphViewer: React.FC<ScreenGraphViewerProps> = ({ data }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [selectedPath, setSelectedPath] = useState<string | null>(null)

  useEffect(() => {
    if (!data || !data.screens) return

    // Convert screen data to React Flow nodes
    const flowNodes: Node[] = data.screens.map((screen: any, index: number) => ({
      id: screen.id,
      type: 'screen',
      position: { 
        x: 200 * (index % 4), 
        y: 150 * Math.floor(index / 4) 
      },
      data: {
        label: screen.name,
        type: screen.type,
        id: screen.id,
      },
    }))

    // Convert connections to React Flow edges
    const flowEdges: Edge[] = data.connections?.map((connection: any) => ({
      id: connection.id,
      source: connection.source,
      target: connection.target,
      type: 'smoothstep',
      animated: connection.type === 'error' ? false : true,
      style: {
        stroke: connection.type === 'error' ? '#ef4444' : '#3b82f6',
        strokeWidth: 2,
        strokeDasharray: connection.type === 'error' ? '5,5' : undefined,
      },
      label: connection.action,
      labelStyle: {
        fontSize: '10px',
        color: '#ffffff',
        backgroundColor: '#1f2937',
        padding: '2px 4px',
        borderRadius: '4px',
      },
    })) || []

    setNodes(flowNodes)
    setEdges(flowEdges)
  }, [data, setNodes, setEdges])

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const highlightPath = (pathName: string) => {
    // This would highlight a specific navigation path
    setSelectedPath(pathName)
    // Implementation would update edge styles to highlight the path
  }

  if (!data) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-dark-400">No screen graph data available</p>
      </div>
    )
  }

  return (
    <div className="h-full flex">
      {/* Path Selector Sidebar */}
      {data.paths && (
        <div className="w-64 border-r border-dark-700 bg-dark-900">
          <div className="p-4 border-b border-dark-700">
            <h4 className="font-medium text-white">Navigation Paths</h4>
            <p className="text-xs text-dark-300 mt-1">
              {data.paths.length} path(s) available
            </p>
          </div>
          
          <div className="overflow-y-auto">
            {data.paths.map((path: any, index: number) => (
              <button
                key={index}
                onClick={() => highlightPath(path.name)}
                className={`
                  w-full p-3 text-left hover:bg-dark-800 transition-colors
                  border-l-2 ${selectedPath === path.name ? 'border-primary-500 bg-dark-800' : 'border-transparent'}
                `}
              >
                <div>
                  <p className="text-sm font-medium text-white">
                    {path.name}
                  </p>
                  <p className="text-xs text-dark-400 mt-1">
                    {path.description}
                  </p>
                  <p className="text-xs text-primary-400 mt-1">
                    {path.steps?.length || 0} steps
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Graph Viewer */}
      <div className="flex-1 bg-dark-950">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-left"
          className="bg-dark-950"
        >
          <Controls 
            className="bg-dark-800 border-dark-600"
            showZoom={true}
            showFitView={true}
            showInteractive={true}
          />
          <MiniMap 
            nodeColor="#374151"
            className="bg-dark-800 border-dark-600"
            maskColor="rgba(0, 0, 0, 0.2)"
          />
          <Background 
            variant={BackgroundVariant.Dots} 
            gap={20} 
            size={1}
            color="#374151"
          />
        </ReactFlow>
      </div>

      {/* Graph Stats */}
      <div className="absolute bottom-4 left-4 bg-dark-900/90 backdrop-blur-sm border border-dark-700 rounded-lg p-3">
        <div className="text-xs text-dark-300 space-y-1">
          <div>Screens: {data.screens?.length || 0}</div>
          <div>Connections: {data.connections?.length || 0}</div>
          <div>Complexity: {data.metadata?.complexity || 'Unknown'}</div>
        </div>
      </div>
    </div>
  )
}
