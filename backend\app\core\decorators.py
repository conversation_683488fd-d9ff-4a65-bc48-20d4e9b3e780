"""
Pipeline step decorator for marking functions as pipeline steps
"""
from typing import Dict, Any, List, Optional, Callable
from functools import wraps
import inspect

# Global registry to store pipeline step metadata
PIPELINE_STEPS_REGISTRY: Dict[str, Dict[str, Any]] = {}

def pipeline_step(
    name: str,
    description: str,
    outputs: List[str],
    inputs: Optional[List[str]] = None,
    configurable_params: Optional[Dict[str, Dict[str, Any]]] = None
):
    """
    Decorator to mark a function as a pipeline step.
    
    Args:
        name: Human-readable name of the step
        description: Description of what the step does
        outputs: List of output artifact names/types
        inputs: List of input artifact names/types (optional)
        configurable_params: Dictionary of configurable parameters with metadata
    
    Example:
        @pipeline_step(
            name="Document Processor",
            description="Processes PDF documents and extracts content",
            outputs=["processed_document.json"],
            inputs=["input_document.pdf"],
            configurable_params={
                "use_ocr": {
                    "type": "boolean",
                    "default": True,
                    "description": "Enable OCR for scanned documents"
                },
                "language": {
                    "type": "select",
                    "options": ["en", "es", "fr"],
                    "default": "en",
                    "description": "Document language"
                }
            }
        )
        def process_document(input_path: str, use_ocr: bool = True, language: str = "en"):
            # Implementation here
            pass
    """
    def decorator(func: Callable) -> Callable:
        # Get function signature for auto-discovery
        sig = inspect.signature(func)
        
        # Auto-discover parameters from function signature
        auto_params = {}
        for param_name, param in sig.parameters.items():
            param_info = {
                "name": param_name,
                "required": param.default == inspect.Parameter.empty,
                "default": param.default if param.default != inspect.Parameter.empty else None,
                "annotation": str(param.annotation) if param.annotation != inspect.Parameter.empty else "Any"
            }
            
            # Infer type from annotation
            if param.annotation == bool:
                param_info["type"] = "boolean"
            elif param.annotation == int:
                param_info["type"] = "number"
            elif param.annotation == float:
                param_info["type"] = "number"
            elif param.annotation == str:
                param_info["type"] = "string"
            else:
                param_info["type"] = "string"  # Default fallback
            
            auto_params[param_name] = param_info
        
        # Merge auto-discovered params with configurable_params
        final_params = auto_params.copy()
        if configurable_params:
            for param_name, param_config in configurable_params.items():
                if param_name in final_params:
                    final_params[param_name].update(param_config)
                else:
                    final_params[param_name] = param_config
        
        # Store metadata in global registry
        step_id = f"{func.__module__}.{func.__name__}"
        PIPELINE_STEPS_REGISTRY[step_id] = {
            "id": step_id,
            "name": name,
            "description": description,
            "function": func,
            "module": func.__module__,
            "function_name": func.__name__,
            "inputs": inputs or [],
            "outputs": outputs,
            "parameters": final_params,
            "configurable_params": configurable_params or {}
        }
        
        # Add metadata to function for easy access
        func._pipeline_step_metadata = PIPELINE_STEPS_REGISTRY[step_id]
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator

def get_pipeline_steps() -> Dict[str, Dict[str, Any]]:
    """Get all registered pipeline steps"""
    return PIPELINE_STEPS_REGISTRY.copy()

def get_pipeline_step(step_id: str) -> Optional[Dict[str, Any]]:
    """Get a specific pipeline step by ID"""
    return PIPELINE_STEPS_REGISTRY.get(step_id)
