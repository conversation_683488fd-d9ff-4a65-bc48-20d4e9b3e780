import ast
from collections import defaultdict
import json
import logging
import re
import os
import threading
from typing import Optional, Dict, Any, List, Tuple, Set
import google.generativeai as generativeai
from google import genai
from google.genai import types
from .rotate_api_key import APIKeyRotator  # Assuming this module exists
from .prompt_storing import (gen_screen_variables_prompt, gen_uc_flow)
from .bfs_pathfinder import BFSPathfinder  # Import BFSPathfinder
from .count_gemini_token import GeminiCostCalculator
from rapidfuzz import fuzz
from pathlib import Path

# Import for graph visualization
import matplotlib.pyplot as plt
import networkx as nx


class ScreenPathGenerator:
    # Class-level tracking for shared files (thread-safe)
    _shared_files_generated = {
        'screen_flow_graph': False
    }
    _shared_files_lock = threading.Lock()
    
    def __init__(self, config_file: str, relevant_content_base_dir: str, business_flows_dir: str, 
                 business_flow_number: int, flows_output_path: str, 
                 screen_paths_output_path: str, base_output_dir: str = None, count_token: bool = False):
        """
        Initialize the class with file paths. User query will be automatically loaded from Business Flow X.txt.
        
        Args:
            config_file: Path to the API configuration file.
            relevant_content_base_dir: Base directory containing relevant_content_processor output.
            business_flows_dir: Directory containing Business Flow X.txt files.
            business_flow_number: Number of the business flow to process (1, 2, 3, etc.)
            flows_output_path: Output path for flows data (business flow specific).
            screen_paths_output_path: Output path for screen paths (business flow specific).
            base_output_dir: Base output directory for shared files. If None, uses parent of flows_output_path.
            count_token: Enable token counting and cost calculation.
        """
        self.config_file = config_file
        self.relevant_content_base_dir = relevant_content_base_dir
        self.business_flows_dir = business_flows_dir
        self.business_flow_number = business_flow_number
        self.flows_output_path = flows_output_path
        self.screen_paths_output_path = screen_paths_output_path
        self.count_token = count_token
        
        # Determine base output directory for shared files
        if base_output_dir is None:
            # Use parent directory of flows_output_path
            self.base_output_dir = os.path.dirname(os.path.dirname(flows_output_path))
        else:
            self.base_output_dir = base_output_dir
        
        # Business flow specific screen variables path
        flow_output_dir = os.path.join(self.base_output_dir, f"business_flow_{business_flow_number}")
        self.business_flow_variables_output_path = os.path.join(flow_output_dir, "screen_variables.json")

        # Shared file paths (generated once for all business flows)
        self.shared_graph_image_output_path = os.path.join(self.base_output_dir, "screen_flow_graph.png")
        
        # Construct paths based on new structure
        self.business_flow_dir = os.path.join(relevant_content_base_dir, f"business_flow_{business_flow_number}")
        self.merged_relevant_content_path = os.path.join(self.business_flow_dir, "merged_relevant_content.json")
        self.screen_graph_path = os.path.join(relevant_content_base_dir, "screen_graph.json")
        self.business_flow_txt_path = os.path.join(business_flows_dir, f"Business Flow {business_flow_number}.txt")
        
        # Load user query from Business Flow X.txt
        self.user_query = self._load_business_flow_content()
        
        # Initialize token tracking
        self.cost_calculator = GeminiCostCalculator() if count_token else None
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_cost = 0.0
        self.model_type = None  # Will be set based on config
        self.api_call_count = 0
        
        self.client = None
        self.relevant_info_cache = None
        self._ensure_output_directories()

        # Load configuration
        self.config = self.load_config(config_file)
        
        # Determine model type for token calculation
        if self.count_token:
            self._determine_model_type()

        # Initialize APIKeyRotator
        self.rotator = APIKeyRotator(config_path=self.config_file)
        
        # Load input data from new structure
        self.relevant_info = self.load_json(self.merged_relevant_content_path)
        self.screen_graph = self.load_json(self.screen_graph_path)
        self.relevant_info_string = json.dumps(self.relevant_info, indent=4, ensure_ascii=False) if self.relevant_info else ""
        self.screen_graph_string = json.dumps(self.screen_graph, indent=4, ensure_ascii=False) if self.screen_graph else ""
        
        # Initialize role detection (defer actual API call to avoid token counting before setup)
        self.detected_roles = {}

    def _get_cache_display_name(self) -> str:
        """
        Get the cache display name for the current business flow.
        
        Returns:
            Cache display name in format: relevant_info_BP_X
        """
        return f"relevant_info_BP_{self.business_flow_number}"

    def _generate_business_flow_screen_variables(self) -> bool:
        """
        Generate screen variables for this specific business flow.

        Returns:
            True if generated successfully or already exists, False otherwise.
        """
        # Check if already exists
        if os.path.exists(self.business_flow_variables_output_path):
            print(f"✅ Screen variables already exist at: {self.business_flow_variables_output_path}")
            return True

        print(f"🔄 Generating screen variables for Business Flow {self.business_flow_number}...")
        try:
            result = self.generate_screen_variables()
            if result:
                print(f"✅ Screen variables generated: {self.business_flow_variables_output_path}")
                return True
            else:
                print("❌ Failed to generate screen variables")
                return False
        except Exception as e:
            print(f"❌ Error generating screen variables: {e}")
            return False

    def _generate_shared_screen_flow_graph(self, screen_flows_dict: Dict[str, Any]) -> bool:
        """
        Generate screen flow graph (shared across all business flows) with thread safety.
        
        Args:
            screen_flows_dict: Screen flows data for visualization
            
        Returns:
            True if generated successfully or already exists, False otherwise.
        """
        with ScreenPathGenerator._shared_files_lock:
            # Check if already generated or file exists
            if (ScreenPathGenerator._shared_files_generated['screen_flow_graph'] or 
                os.path.exists(self.shared_graph_image_output_path)):
                print(f"✅ Screen flow graph already exists at: {self.shared_graph_image_output_path}")
                ScreenPathGenerator._shared_files_generated['screen_flow_graph'] = True
                return True
            
            print("🔄 Generating shared screen flow graph...")
            try:
                if screen_flows_dict:
                    self.visualize_screen_graph(screen_flows_dict, self.shared_graph_image_output_path)
                    ScreenPathGenerator._shared_files_generated['screen_flow_graph'] = True
                    print(f"✅ Screen flow graph generated: {self.shared_graph_image_output_path}")
                    return True
                else:
                    print("⚠️ No screen flows data for visualization")
                    return False
            except Exception as e:
                print(f"❌ Error generating screen flow graph: {e}")
                return False

    @classmethod
    def reset_shared_files_tracking(cls):
        """Reset the shared files tracking (useful for testing or re-running)."""
        with cls._shared_files_lock:
            cls._shared_files_generated['screen_flow_graph'] = False
            print("🔄 Reset shared files tracking")

    def _load_business_flow_content(self) -> str:
        """
        Load user query content from Business Flow X.txt file.
        
        Returns:
            Content of the business flow file as string, or default message if file not found.
        """
        try:
            if os.path.exists(self.business_flow_txt_path):
                with open(self.business_flow_txt_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        print(f"✅ Loaded user query from: {self.business_flow_txt_path}")
                        return content
                    else:
                        print(f"⚠️ Business flow file is empty: {self.business_flow_txt_path}")
                        return f"Business flow {self.business_flow_number} process"
            else:
                print(f"⚠️ Business flow file not found: {self.business_flow_txt_path}")
                return f"Business flow {self.business_flow_number} process"
        except Exception as e:
            print(f"❌ Error loading business flow file {self.business_flow_txt_path}: {e}")
            return f"Business flow {self.business_flow_number} process"

    @classmethod
    def create_for_business_flow(cls, config_file: str, relevant_content_base_dir: str, 
                                business_flows_dir: str, business_flow_number: int, 
                                base_output_dir: str, count_token: bool = False):
        """
        Factory method to create ScreenPathGenerator for a specific business flow.
        User query will be automatically loaded from Business Flow X.txt.
        
        Args:
            config_file: Path to the API configuration file.
            relevant_content_base_dir: Base directory containing relevant_content_processor output.
            business_flows_dir: Directory containing Business Flow X.txt files.
            business_flow_number: Number of the business flow to process.
            base_output_dir: Base directory for output files.
            count_token: Enable token counting and cost calculation.
            
        Returns:
            ScreenPathGenerator instance configured for the specified business flow.
        """
        # Create output paths for this business flow
        flow_output_dir = os.path.join(base_output_dir, f"business_flow_{business_flow_number}")
        
        flows_output_path = os.path.join(flow_output_dir, "flows_data.json")
        screen_paths_output_path = os.path.join(flow_output_dir, "screen_paths.json")
        
        return cls(
            config_file=config_file,
            relevant_content_base_dir=relevant_content_base_dir,
            business_flows_dir=business_flows_dir,
            business_flow_number=business_flow_number,
            flows_output_path=flows_output_path,
            screen_paths_output_path=screen_paths_output_path,
            base_output_dir=base_output_dir,
            count_token=count_token
        )

    @staticmethod
    def process_all_business_flows(config_file: str, relevant_content_base_dir: str, 
                                  business_flows_dir: str, base_output_dir: str, 
                                  count_token: bool = False):
        """
        Process all business flows found in the relevant_content_base_dir.
        User queries will be automatically loaded from Business Flow X.txt files.
        
        Args:
            config_file: Path to the API configuration file.
            relevant_content_base_dir: Base directory containing relevant_content_processor output.
            business_flows_dir: Directory containing Business Flow X.txt files.
            base_output_dir: Base directory for output files.
            count_token: Enable token counting and cost calculation.
            
        Returns:
            List of results from processing each business flow.
        """
        results = []
        
        # Find all business_flow directories
        if not os.path.exists(relevant_content_base_dir):
            print(f"Error: Relevant content base directory not found: {relevant_content_base_dir}")
            return results
            
        business_flow_dirs = [d for d in os.listdir(relevant_content_base_dir) 
                             if d.startswith("business_flow_") and 
                             os.path.isdir(os.path.join(relevant_content_base_dir, d))]
        
        if not business_flow_dirs:
            print(f"No business flow directories found in {relevant_content_base_dir}")
            return results
            
        business_flow_dirs.sort()  # Process in order
        
        total_tokens = {"input": 0, "output": 0, "cost": 0.0}
        
        for business_flow_dir in business_flow_dirs:
            # Extract business flow number
            match = re.search(r"business_flow_(\d+)", business_flow_dir)
            if not match:
                continue
                
            business_flow_number = int(match.group(1))
            print(f"\n{'='*50}")
            print(f"Processing Business Flow {business_flow_number}")
            print(f"{'='*50}")
            
            try:
                # Create processor for this business flow
                processor = ScreenPathGenerator.create_for_business_flow(
                    config_file=config_file,
                    relevant_content_base_dir=relevant_content_base_dir,
                    business_flows_dir=business_flows_dir,
                    business_flow_number=business_flow_number,
                    base_output_dir=base_output_dir,
                    count_token=count_token
                )
                
                # Execute processing
                processor.execute()
                
                # Collect token summary if enabled
                token_summary = processor.get_token_summary() if count_token else {}
                if token_summary and token_summary.get("token_counting_enabled"):
                    total_tokens["input"] += token_summary.get("total_input_tokens", 0)
                    total_tokens["output"] += token_summary.get("total_output_tokens", 0)
                    total_tokens["cost"] += token_summary.get("total_cost_usd", 0.0)
                
                results.append({
                    "business_flow_number": business_flow_number,
                    "success": True,
                    "token_summary": token_summary,
                    "output_paths": {
                        "flows": processor.flows_output_path,
                        "paths": processor.screen_paths_output_path,
                        "variables": processor.business_flow_variables_output_path
                    }
                })
                
                print(f"✅ Business Flow {business_flow_number} processed successfully")
                
            except Exception as e:
                print(f"❌ Error processing Business Flow {business_flow_number}: {e}")
                results.append({
                    "business_flow_number": business_flow_number,
                    "success": False,
                    "error": str(e)
                })
        
        # Print overall summary
        if count_token and total_tokens["input"] > 0:
            print(f"\n{'='*50}")
            print("OVERALL TOKEN SUMMARY")
            print(f"{'='*50}")
            print(f"Total Input Tokens: {total_tokens['input']:,}")
            print(f"Total Output Tokens: {total_tokens['output']:,}")
            print(f"Total Tokens: {total_tokens['input'] + total_tokens['output']:,}")
            print(f"Total Cost: ${total_tokens['cost']:.6f}")
            print(f"Business Flows Processed: {len([r for r in results if r['success']])}")
        
        # Add shared files info
        if results:
            first_processor = None
            for result in results:
                if result.get('success'):
                    # Get shared file paths from any successful processor
                    processor = ScreenPathGenerator.create_for_business_flow(
                        config_file=config_file,
                        relevant_content_base_dir=relevant_content_base_dir,
                        business_flows_dir=business_flows_dir,
                        business_flow_number=result['business_flow_number'],
                        base_output_dir=base_output_dir,
                        count_token=False
                    )
                    first_processor = processor
                    break
            
            if first_processor:
                print(f"\n{'='*50}")
                print("SHARED FILES (generated once for all business flows)")
                print(f"{'='*50}")
                print(f"📈 Screen Flow Graph: {first_processor.shared_graph_image_output_path}")
                print(f"\n{'='*50}")
                print("BUSINESS FLOW SPECIFIC FILES")
                print(f"{'='*50}")
                print("📊 Screen Variables: Generated separately for each business flow in their respective directories")
        
        return results

    @staticmethod
    def process_from_base_dir(config_file: str, base_dir: str, output_dir: str = None, 
                             count_token: bool = False):
        """
        Convenience method to process all business flows from a base directory.
        Automatically detects business_flows/ and relevant_content_processor/ subdirectories.
        
        Args:
            config_file: Path to the API configuration file.
            base_dir: Base directory containing both business_flows/ and relevant_content_processor/
            output_dir: Output directory. If None, uses base_dir/path_processor/
            count_token: Enable token counting and cost calculation.
            
        Returns:
            List of results from processing each business flow.
        """
        business_flows_dir = os.path.join(base_dir, "business_flows")
        relevant_content_base_dir = os.path.join(base_dir, "relevant_content_processor")
        
        if output_dir is None:
            output_dir = os.path.join(base_dir, "path_processor")
        
        # Validate paths exist
        if not os.path.exists(business_flows_dir):
            print(f"❌ Business flows directory not found: {business_flows_dir}")
            return []
            
        if not os.path.exists(relevant_content_base_dir):
            print(f"❌ Relevant content directory not found: {relevant_content_base_dir}")
            return []
        
        print(f"📁 Auto-detected directories:")
        print(f"  Business Flows: {business_flows_dir}")
        print(f"  Relevant Content: {relevant_content_base_dir}")
        print(f"  Output: {output_dir}")
        
        return ScreenPathGenerator.process_all_business_flows(
            config_file=config_file,
            relevant_content_base_dir=relevant_content_base_dir,
            business_flows_dir=business_flows_dir,
            base_output_dir=output_dir,
            count_token=count_token
        )

    def _determine_model_type(self) -> None:
        """Xác định loại model (pro hay flash) dựa trên config file."""
        if not self.config:
            return
            
        model_name = self.config.get("config_gemini_model", {}).get("model_name", "").lower()
        
        if "flash" in model_name:
            self.model_type = "flash"
        elif "pro" in model_name:
            self.model_type = "pro"
        else:
            # Default to pro if unclear
            self.model_type = "pro"
            
        if self.count_token:
            print(f"Token counting enabled for model type: {self.model_type}")

    def _calculate_token_cost(self, input_tokens: int, output_tokens: int) -> Dict[str, Any]:
        """
        Tính toán chi phí token dựa trên model type.
        
        Args:
            input_tokens: Số token đầu vào
            output_tokens: Số token đầu ra
            
        Returns:
            Dict chứa thông tin chi phí
        """
        if not self.cost_calculator or not self.model_type:
            return {}
            
        if self.model_type == "flash":
            return self.cost_calculator.calculate_flash_cost(input_tokens, output_tokens)
        else:
            return self.cost_calculator.calculate_pro_cost(input_tokens, output_tokens)

    def get_token_summary(self) -> Dict[str, Any]:
        """
        Trả về tổng kết chi phí token.
        
        Returns:
            Dict chứa thông tin tổng kết về token và chi phí
        """
        if not self.count_token or not self.cost_calculator:
            return {"token_counting_enabled": False}
            
        total_cost_info = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens)
        
        return {
            "token_counting_enabled": True,
            "model_type": self.model_type,
            "total_input_tokens": self.total_input_tokens,
            "total_output_tokens": self.total_output_tokens,
            "total_tokens": self.total_input_tokens + self.total_output_tokens,
            "api_call_count": self.api_call_count,
            **total_cost_info
        }

    def print_token_summary(self) -> None:
        """In tổng kết chi phí token và cost."""
        if not self.count_token or not self.cost_calculator:
            return
            
        token_summary = self.get_token_summary()
        self.cost_calculator.print_token_summary(token_summary, "SCREEN PATH GENERATOR")
        
        # Log billing information
        from .billing_logger import billing_logger
        billing_logger.log_module_cost("ScreenPathGenerator", token_summary)

    def load_config(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Load configuration from JSON file."""
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                return json.load(file)
        except Exception as e:
            print(f"Error loading config: {e}")
            return None

    def load_json(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Load JSON data from file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Error: File not found at {file_path}")
            return None
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from {file_path}: {e}")
            return None
        except Exception as e:
            print(f"An unexpected error occurred loading JSON from {file_path}: {e}")
            return None

    def rotate_api_key(self):
        """Rotate API key and reconfigure model."""
        self.api_key = self.rotator.get_api_key()
        self.rotator.rotate_api_key()
        generativeai.configure(api_key=self.api_key)
        if self.config and "config_gemini_model" in self.config:
            model_name = self.config["config_gemini_model"].get("model_name", "gemini-1.5-pro")
            self.model = generativeai.GenerativeModel(
                model_name,
                generation_config={"response_mime_type": "application/json"}
            )
            self.client = genai.Client(api_key=self.api_key)
            
            # Look for cache with business flow specific name
            cache_display_name = self._get_cache_display_name()
            for cache in self.client.caches.list():
                if (cache.display_name == cache_display_name):
                    self.relevant_info_cache = cache
                    print(f"Receive cache successfully: {self.relevant_info_cache} (display_name: {cache_display_name})")
                    break
            
            if not self.relevant_info_cache:
                print(f"⚠️ Cache not found with display_name: {cache_display_name}")
                print("Available caches:")
                for cache in self.client.caches.list():
                    print(f"  - {cache.display_name}")
            else:
                print(f"✅ Using cache: {cache_display_name} for business flow {self.business_flow_number}")
        else:
            print("Error: Gemini configuration not found or incomplete. Cannot initialize model.")
            self.model = None
            self.client = None

    def _ensure_output_directories(self) -> None:
        """Đảm bảo rằng các thư mục output tồn tại."""
        output_dirs = set(os.path.dirname(path) for path in [
            self.flows_output_path,
            self.screen_paths_output_path,
            self.business_flow_variables_output_path,
            self.shared_graph_image_output_path
        ])
        for output_dir in output_dirs:
            if output_dir and not os.path.exists(output_dir):  # Kiểm tra nếu thư mục không rỗng và chưa tồn tại
                os.makedirs(output_dir)
                logging.info(f"Created output directory: {output_dir}")

    def parse_description(self, description_str: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Parse the custom description string into objects and connections."""
        if not isinstance(description_str, str):
            print(f"Error: Description is not a string: {description_str}")
            return [], []
        
        try:
            # Split the string into 'Objects' and 'Connections' parts
            parts = description_str.split("; ")
            if len(parts) != 2:
                print(f"Error: Expected two parts separated by '; ', but found {len(parts)} parts.")
                return [], []
            
            objects_part = parts[0].strip()
            connections_part = parts[1].strip()
            
            # Verify the parts start with expected labels
            if not objects_part.startswith("Objects:") or not connections_part.startswith("Connections:"):
                print("Error: Parts do not start with expected labels.")
                return [], []
            
            # Extract the list strings after the labels
            _, objects_str = objects_part.split(": ", 1)
            _, connections_str = connections_part.split(": ", 1)
            
            # Parse the list strings into Python lists using ast.literal_eval
            objects = ast.literal_eval(objects_str)
            connections = ast.literal_eval(connections_str)
            
            # Ensure the parsed results are lists
            if not isinstance(objects, list):
                print(f"Error: Parsed objects is not a list. Type: {type(objects)}")
                objects = []
            if not isinstance(connections, list):
                print(f"Error: Parsed connections is not a list. Type: {type(connections)}")
                connections = []
                
        except Exception as e:
            print(f"Error parsing description: {e}. Description snippet: {description_str[:100]}...")
            return [], []
            
        return objects, connections

    def generate_screen_flows_algorithmically(self, entry_screen: str) -> Dict[str, Dict[str, Any]]:
        """
        Generate screen flows algorithmically from screen_graph.json using the new graph format.
        
        Args:
            entry_screen: The entry screen name.
        
        Returns:
            A dictionary of screen flows.
        """
        if not self.screen_graph:
            print("Error: screen_graph is not loaded.")
            return {}

        # Check if we have the new graph format
        if "graph" in self.screen_graph and "nodes" in self.screen_graph["graph"]:
            # Process the new graph format
            graph = self.screen_graph["graph"]
            nodes = graph["nodes"]
            edges = graph["edges"]
            
            # Extract screen names and create screen_flows
            screens = {}
            for node in nodes:
                if node.get("type") == "screen":
                    # Use lowercase IDs for consistency
                    screen_id = node["name"]
                    screens[screen_id] = {
                        "screen_name": screen_id,
                        "use_case_related": None,
                        "next_screen": None,
                        "before_screen": None
                    }
            
            # Process edges to determine next_screen and before_screen
            for edge in edges:
                source = edge["source"]
                target = edge["target"]
                
                # Convert source/target IDs to the screen names
                source_name = next((node["name"] for node in nodes if node["id"] == source), source)
                target_name = next((node["name"] for node in nodes if node["id"] == target), target)
               

                
                # Set next_screen if it's not already set (we'll take the first one we find)
                if source_name in screens and not screens[source_name]["next_screen"]:
                    screens[source_name]["next_screen"] = target_name
                
                # Set before_screen if it's not already set (we'll take the first one we find)
                if target_name in screens and not screens[target_name]["before_screen"]:
                    screens[target_name]["before_screen"] = source_name
            
            return screens
        else:
            # Fallback to the old logic for backward compatibility
            screens = set()
            all_connections = []
            
            for screen_flow_item in self.screen_graph.get("screens", []):
                description = screen_flow_item.get("Description")
                if not isinstance(description, str):
                    print(f"Warning: 'Description' field is missing or not a string for screen_flow_item: {screen_flow_item}")
                    continue
                
                objects, connections = self.parse_description(description) 
                
                for obj in objects:
                    if isinstance(obj, dict) and "object_name" in obj:
                        screens.add(obj["object_name"])
                    else:
                        print(f"Warning: Invalid object format in description: {obj}")
                all_connections.extend(connections)
            
            next_screens = defaultdict(list)
            before_screens = defaultdict(list)
            
            for conn in all_connections:
                source = conn.get("source")
                target = conn.get("target")
                if not source or not target:
                    print(f"Warning: Invalid connection format (missing source or target): {conn}")
                    continue
                
                screens.add(source) # Ensure all connected screens are in the set
                screens.add(target)

                next_screens[source].append(target)
                if target != entry_screen:
                    before_screens[target].append(source)
            
            screen_flows = {}
            if entry_screen: # Ensure entry_screen is added if provided
                 screens.add(entry_screen)

            for screen_name in sorted(list(screens)):
                screen_flows[screen_name] = {
                    "screen_name": screen_name,
                    "use_case_related": None,
                    "next_screen": next_screens[screen_name][0] if next_screens[screen_name] else None,
                    "before_screen": before_screens[screen_name][0] if before_screens[screen_name] else None
                }
            
            return screen_flows

    def extract_screen_to_uc(self) -> Dict[str, str]:
        """
        Extract mapping from screen names to use case IDs from relevant_info.json.

        Returns:
            A dictionary mapping screen names to use case IDs.
        """
        screen_to_uc = {}
        if not self.relevant_info:
            print("Error: relevant_info is not loaded.")
            return screen_to_uc

        for uc in self.relevant_info:
            screen_related = uc.get("Screen related", [])
            if isinstance(screen_related, str):
                screen_related = [screen_related]
            for screen in screen_related:
                if screen not in screen_to_uc:
                    screen_to_uc[screen] = uc["id"]

        return screen_to_uc

    def _find_cache_by_display_name(self, display_name: str):
        """
        Find cache by display name.

        Args:
            display_name: The display name of the cache to find

        Returns:
            Cache object if found, None otherwise
        """
        if not self.client:
            print(f"⚠️ Client not initialized, cannot find cache with display name: {display_name}")
            return None

        try:
            for cache in self.client.caches.list():
                if cache.display_name == display_name:
                    print(f"✅ Found cache with display name: {display_name}")
                    return cache

            print(f"⚠️ Cache not found with display name: {display_name}")
            print("Available caches:")
            for cache in self.client.caches.list():
                print(f"  - {cache.display_name}")
            return None

        except Exception as e:
            print(f"❌ Error searching for cache: {e}")
            return None
    
    def _extract_navigation_rules_from_graph(self) -> Dict[str, str]:
        """
        Extract navigation rules algorithmically from screen_graph.

        Returns:
            Dictionary with navigation rules in format "(Source, Target)": "Element"
        """
        navigation_rules = {}

        if not self.screen_graph:
            print("No screen_graph available for navigation rules extraction.")
            return navigation_rules

        try:
            # Check if we have the new graph format
            if "graph" in self.screen_graph and "edges" in self.screen_graph["graph"]:
                edges = self.screen_graph["graph"]["edges"]
                nodes = self.screen_graph["graph"]["nodes"]

                # Create a mapping from node ID to node name
                node_id_to_name = {}
                for node in nodes:
                    if node.get("type") == "screen":
                        node_id_to_name[node["id"]] = node["name"]

                # Process edges to create navigation rules
                for edge in edges:
                    source_id = edge.get("source")
                    target_id = edge.get("target")
                    element = edge.get("element", "Navigate")

                    # Get screen names from node IDs
                    source_name = node_id_to_name.get(source_id, source_id)
                    target_name = node_id_to_name.get(target_id, target_id)

                    if source_name and target_name:
                        rule_key = f"({source_name}, {target_name})"

                        # Handle multiple elements for the same source-target pair
                        if rule_key in navigation_rules:
                            existing_element = navigation_rules[rule_key]
                            if element not in existing_element:
                                navigation_rules[rule_key] = f"{existing_element} or {element}"
                        else:
                            navigation_rules[rule_key] = element

                print(f"✅ Extracted {len(navigation_rules)} navigation rules from screen graph")

            else:
                print("⚠️ Screen graph format not supported for automatic navigation rules extraction")

        except Exception as e:
            print(f"❌ Error extracting navigation rules: {e}")

        return navigation_rules

    def generate_screen_variables(self) -> Optional[str]:
        """Generate screen variables from input data and save to file."""
        if not self.relevant_info or not self.screen_graph:
            print("No valid data initialized for screen variables generation.")
            return None

        # Step 1: Extract navigation rules algorithmically
        print("🔄 Extracting navigation rules from screen graph...")
        navigation_rules = self._extract_navigation_rules_from_graph()

        # Modified prompt to focus only on screen_definitions for this specific business flow
        prompt = f"""
            {gen_screen_variables_prompt}

            -----BEGIN Business Flow Context-----
            Business Flow Number: {self.business_flow_number}
            Business Flow Description: {self.user_query}

            IMPORTANT FILTERING INSTRUCTIONS:
            1. Only extract screen variables for screens that are directly relevant to this specific business flow description.
            2. Do NOT extract variables for screens that are not mentioned, referenced, or logically involved in this business flow.
            3. If a screen is not related to the business flow context, skip it entirely - do not create empty entries.
            4. Focus only on screens that users would actually interact with during this specific business process.
            5. Use the business flow description above to determine which screens are relevant.

            The relevant_info cache contains detailed information specifically filtered for this business flow.
            Use this context to identify which screens from the screen graph are actually needed.
            -----END Business Flow Context-----

            -----BEGIN Screen Flow Information input information-----
            {self.screen_graph_string}
            -----END Screen Flow Information input information-----
    """
        try:
            self.rotate_api_key()
            if not self.model:
                print("Model not initialized after key rotation. Cannot generate screen variables.")
                return None

            # Find the relevant_info_BP_X cache for this business flow
            cache_display_name = self._get_cache_display_name()
            relevant_info_cache = self._find_cache_by_display_name(cache_display_name)
            if not relevant_info_cache:
                print(f"❌ Cannot find cache with display name '{cache_display_name}'. Screen variables generation requires this cache.")
                return None

            # Count input tokens if enabled (using simple token counting since client API is used)
            input_tokens = 0
            if self.count_token:
                try:
                    # input_tokens = self.client.models.count_tokens(contents=prompt, model="gemini-2.5-pro")
                    print(f"Screen Variables - Estimated input tokens:")
                except Exception as e:
                    print(f"Failed to estimate input tokens: {e}")

            print(f"🔄 Using cache '{cache_display_name}' for screen definitions generation...")
            response = self.client.models.generate_content(
                model="gemini-2.5-pro",
                contents=prompt,
                config=types.GenerateContentConfig(
                cached_content=relevant_info_cache.name
                )
            )

            # Count output tokens if enabled
            output_tokens = 0
            if self.count_token:
                try:
                    # Estimate output tokens from response text
                    output_tokens = len(response.text.split()) * 1.3  # Rough estimation
                    print(f"Screen Variables - Estimated output tokens: {int(output_tokens):,}")
                except Exception as e:
                    print(f"Failed to estimate output tokens: {e}")

            # Track tokens if enabled and successful
            if self.count_token and input_tokens > 0:
                self.total_input_tokens += int(input_tokens)
                self.total_output_tokens += int(output_tokens)
                self.api_call_count += 1

                # Calculate cost for this request
                cost_info = self._calculate_token_cost(int(input_tokens), int(output_tokens))

                print(f"Screen Variables - Token usage - Input: {int(input_tokens):,}, Output: {int(output_tokens):,}")
                if cost_info:
                    print(f"Screen Variables - Request cost: ${cost_info.get('total_cost_usd', 0):.6f}")
                    cumulative_cost = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens).get('total_cost_usd', 0)
                    print(f"Screen Variables - Cumulative cost: ${cumulative_cost:.6f}")

            # Extract and parse LLM response for screen_definitions
            match = re.search(r"```(?:json|python)?(.*?)```", response.text, re.DOTALL)
            extracted_text = match.group(1).strip() if match else response.text.strip()

            try:
                # Parse the JSON response to get screen_definitions
                llm_response = json.loads(extracted_text)
                screen_definitions = llm_response.get("screen_definitions", [])

                print(f"✅ LLM extracted {len(screen_definitions)} screen definitions")

                # Step 3: Combine screen_definitions and navigation_rules
                final_result = {
                    "screen_definitions": screen_definitions,
                    "navigation_rules": navigation_rules
                }

                # Ensure output directory exists
                os.makedirs(os.path.dirname(self.business_flow_variables_output_path), exist_ok=True)

                # Save combined result to file
                with open(self.business_flow_variables_output_path, "w", encoding="utf-8") as outfile:
                    json.dump(final_result, outfile, indent=4, ensure_ascii=False)

                print(f"✅ Screen variables saved to {self.business_flow_variables_output_path}")
                print(f"📊 Final result contains:")
                print(f"   - {len(screen_definitions)} screen definitions")
                print(f"   - {len(navigation_rules)} navigation rules")

                return json.dumps(final_result, indent=4, ensure_ascii=False)

            except json.JSONDecodeError as e:
                print(f"❌ Error parsing JSON response: {e}")
                print(f"Raw response: {response.text}")
                return None
        except Exception as e:
            print(f"Error generating screen variables: {e}")
            return None
    


    def extract_screen_names_from_graph(self) -> List[str]:
        """
        Extract all screen names from the screen graph.
        
        Returns:
            A list of screen names from the graph.
        """
        screen_names = []
        
        if not self.screen_graph:
            print("Error: screen_graph is not loaded.")
            return screen_names
            
        # Check if we have the new graph format
        if "graph" in self.screen_graph and "nodes" in self.screen_graph["graph"]:
            # Process the new graph format
            graph = self.screen_graph["graph"]
            nodes = graph["nodes"]
            
            # Extract screen names
            for node in nodes:
                if node.get("type") == "screen":
                    screen_names.append(node["name"])
        else:
            # Fallback to the old logic for backward compatibility
            for screen_flow_item in self.screen_graph.get("screens", []):
                description = screen_flow_item.get("Description")
                if not isinstance(description, str):
                    continue
                
                objects, _ = self.parse_description(description) 
                
                for obj in objects:
                    if isinstance(obj, dict) and "object_name" in obj:
                        screen_names.append(obj["object_name"])
        
        return screen_names

    def find_best_matching_screen(self, target_screen_name: str, available_screens: List[str], threshold: int = 85) -> Optional[str]:
        """
        Find the best matching screen name using fuzzy string matching.
        
        Args:
            target_screen_name: The screen name to match.
            available_screens: List of available screen names to match against.
            threshold: Minimum similarity score (0-100) to consider a match.
            
        Returns:
            The best matching screen name or None if no good match found.
        """
        if not available_screens:
            return None
            
        best_match = None
        best_score = 0
        
        for screen in available_screens:
            # Calculate similarity score
            score = fuzz.ratio(target_screen_name, screen)
            
            # If this is a better match than what we've seen so far
            if score > best_score:
                best_score = score
                best_match = screen
        
        # Only return the match if it's above our threshold
        if best_score >= threshold:
            if best_score < 100:
                print(f"Fuzzy matched '{target_screen_name}' to '{best_match}' with score {best_score}")
            return best_match
        else:
            print(f"No good match found for '{target_screen_name}'. Best candidate was '{best_match}' with score {best_score}")
            return None

    def generate_flows(self) -> Optional[Dict[str, Any]]:
        """Generate screen flows and use case flows, then combine them."""
        if not self.screen_graph or not self.relevant_info:
            print("No valid data initialized for flows generation.")
            return None

        

        prompt = f"""
            {gen_uc_flow}

            Input Data:
                **Business process:**\n
                -----BEGIN BUSINESS PROCESS---------\n
                \n{self.user_query}\n
                -----END BUSINESS PROCESS---------

                **USE CASE DATA (relevant_info content):**\n
                **SCREEN FLOW DATA (screen_graph.json content):**\n
                -----BEGIN SCREEN GRAPH DATA-----\n
                {self.screen_graph}\n
                -----END SCREEN GRAPH DATA-----\n
        """
        try:
            self.rotate_api_key()  
            input_tokens = 0
            if self.count_token:
                try:
                    # input_tokens = self.client.models.count_tokens(contents=prompt, model="gemini-2.5-pro")
                    print(f"Generate Flows - Estimated input tokens prompt:")
                except Exception as e:
                    print(f"Failed to estimate input tokens: {e}")
            
            response = self.client.models.generate_content(
                model="gemini-2.5-pro", 
                contents=prompt,
                config=types.GenerateContentConfig(
                cached_content=self.relevant_info_cache.name
                )
            )
            
            # Count output tokens if enabled
            output_tokens = 0
            if self.count_token:
                try:
                    # Estimate output tokens from response text
                    output_tokens = len(response.text.split()) * 1.3  # Rough estimation
                    print(f"Generate Flows - Estimated output tokens: {int(output_tokens):,}")
                except Exception as e:
                    print(f"Failed to estimate output tokens: {e}")
            
            # Track tokens if enabled and successful
            if self.count_token and input_tokens > 0:
                self.total_input_tokens += int(input_tokens)
                self.total_output_tokens += int(output_tokens)
                self.api_call_count += 1
                
                # Calculate cost for this request
                cost_info = self._calculate_token_cost(int(input_tokens), int(output_tokens))
                
                print(f"Generate Flows - Token usage - Input: {int(input_tokens):,}, Output: {int(output_tokens):,}")
                if cost_info:
                    print(f"Generate Flows - Request cost: ${cost_info.get('total_cost_usd', 0):.6f}")
                    cumulative_cost = self._calculate_token_cost(self.total_input_tokens, self.total_output_tokens).get('total_cost_usd', 0)
                    print(f"Generate Flows - Cumulative cost: ${cumulative_cost:.6f}")
            
            match = re.search(r"```(?:json|python)?(.*?)```", response.text, re.DOTALL)
            extracted_text = match.group(1).strip() if match else response.text.strip()
            data = json.loads(extracted_text)
            use_case_flows = data.get("use_case_flows", [])
            
            # Extract all screen names from the screen graph
            available_screen_names = self.extract_screen_names_from_graph()
            
            # Normalize screen names in use case flows using fuzzy matching
            for uc in use_case_flows:
                if "screen_belong" in uc:
                    original_screen_name = uc["screen_belong"]
                    # Find best matching screen name from the graph
                    matched_screen_name = self.find_best_matching_screen(
                        original_screen_name, 
                        available_screen_names
                    )
                    
                    if matched_screen_name:
                        uc["screen_belong"] = matched_screen_name
                    else:
                        print(f"Warning: No matching screen found for '{original_screen_name}' in use case {uc.get('id', 'unknown')}")

            screen_flows = self.generate_screen_flows_algorithmically(entry_screen=use_case_flows[0]["screen_belong"])
            
            if not isinstance(use_case_flows, list):
                print("Error: 'use_case_flows' is not a list in LLM output.")
                return None
        except Exception as e:
            print(f"Error generating use_case_flows: {e}")
            return None

        # Duyệt qua use_case_flows để cập nhật use_case_related trong screen_flows
        for uc in use_case_flows:
            if "screen_belong" in uc and "id" in uc:
                screen_name = uc["screen_belong"]
                if screen_name in screen_flows:
                    screen_flows[screen_name]["use_case_related"] = uc["id"]
                else:
                    print(f"Screen '{screen_name}' not found in screen_flows")
            else:
                print(f"Missing key in use_case: {uc}")

        final_output_data = {
            "screen_flows": screen_flows,
            "use_case_flows": use_case_flows
        }

        with open(self.flows_output_path, "w", encoding="utf-8") as outfile:
            json.dump(final_output_data, outfile, indent=4, ensure_ascii=False)
        print(f"Flows saved to {self.flows_output_path}")
        return final_output_data



    def build_paths_with_bfs(self, flows_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Build screen paths using BFS algorithm for optimal pathfinding.
        Uses the new screen graph structure for efficient path discovery.
        """
        use_case_flows_list = flows_data.get('use_case_flows')
        screen_flows_dict = flows_data.get('screen_flows', {})
        if not use_case_flows_list:
            print("Warning: Missing 'use_case_flows' list. Cannot build BFS paths.")
            return []
            
        # Create a temporary file with the graph in the proper format for BFSPathfinder
        temp_graph_path = os.path.join(os.path.dirname(self.screen_paths_output_path), "temp_graph_for_bfs.json")
        
        try:
            # Save the graph to a temporary file
            with open(temp_graph_path, 'w', encoding='utf-8') as f:
                json.dump(self.screen_graph, f, indent=2, ensure_ascii=False)
            
            # Use detected roles
            role_map = self.detected_roles
            
            # Extract available roles from the role map
            available_roles = list(role_map.values())
            print(f"Available roles for BFS pathfinding: {available_roles}")
            
            # Get node[1] from screen graph to use as the default starting point
            node1_name = None
            if "graph" in self.screen_graph and "nodes" in self.screen_graph["graph"]:
                nodes = self.screen_graph["graph"]["nodes"]
                if len(nodes) > 1:
                    # Get the second node (index 1)
                    node1 = nodes[1]
                    node1_name = node1.get("name")
                    print(f"Found node[1] from screen graph: {node1_name}")
                elif len(nodes) > 0:
                    # Fallback to first node if there's no second node
                    node1 = nodes[0]
                    node1_name = node1.get("name")
                    print(f"Falling back to node[0] from screen graph: {node1_name}")
            
            # Now generate paths for each use case sequence
            paths_result = []
            
            for i, uc in enumerate(use_case_flows_list):
                uc_id = uc.get("id")
                target_screen = uc.get("screen_belong")
                
                if not target_screen:
                    print(f"Warning: No screen found for use case '{uc_id}'. Skipping.")
                    continue
                    
                # Process all use cases including duplicates (differentiated by role)
                print(f"Processing use case {i+1}/{len(use_case_flows_list)}: {uc_id} (Role: {uc.get('Role', 'Unknown')})")
                
                # Extract role from the use case flow data
                role = uc.get("Role")
                if role:
                    print(f"Extracted role '{role}' from use case flow data for '{uc_id}'")
                else:
                    print(f"Warning: No role found in use case flow data for '{uc_id}'")
                
                if role:
                    login_action_filter = f"(as {role})"
                    print(f"Finding path for {uc_id} with role: {role}, login filter: {login_action_filter}")
                else:
                    print(f"Warning: Could not determine role for use case {uc_id}. Using default.")
                    login_action_filter = None  # Use any login path

                # Determine the start screen based on the use case
                use_case_flows_list = flows_data.get('use_case_flows')
                entry_screen = use_case_flows_list[0]["screen_belong"]
                
                # Check if this is a login use case by screen name
                if target_screen and "login" in target_screen.lower():
                    start_screen = entry_screen  # For login use cases, start from login page
                    print(f"Using entry screen for login use case (detected by screen): '{start_screen}' for {uc_id}")
                else:  # All non-login use cases
                    # First try to use previous use case's target screen as starting point
                    if i > 0 and "screen_belong" in use_case_flows_list[i-1]:
                        start_screen = use_case_flows_list[i-1]["screen_belong"]
                        print(f"Using previous screen as start screen: '{start_screen}' for {uc_id}")
                    # If first use case or previous has no screen_belong, use entry_screen
                    elif entry_screen:
                        start_screen = entry_screen
                        print(f"Using entry screen for use case: '{start_screen}' for {uc_id}")
                    # Final fallback
                    else:
                        # Ensure start_screen is always set without hardcoding
                        if node1_name:
                            start_screen = node1_name
                            print(f"Using node[1] as fallback start screen: '{start_screen}' for {uc_id}")
                        elif len(self.screen_graph.get("graph", {}).get("nodes", [])) > 0:
                            # Use the first available node if node1 isn't available
                            first_node = self.screen_graph["graph"]["nodes"][0]
                            start_screen = first_node.get("name")
                            print(f"Using first available node as fallback: '{start_screen}' for {uc_id}")
                        else:
                            # Last resort: use the use case's own screen as both start and target
                            start_screen = target_screen
                            print(f"Using target screen as fallback start: '{start_screen}' for {uc_id}")
                
                # Find the shortest path
                print(f"Searching for path from '{start_screen}' to '{target_screen}'")
                path = BFSPathfinder.find_shortest_path(
                    graph_json_path=temp_graph_path,
                    start_screen=start_screen,
                    target_screen=target_screen,
                    login_action_filter=login_action_filter,
                    available_roles=available_roles
                )

                if path:
                    paths_result.append({
                        "use_case_id": uc_id,
                        "screen": target_screen,
                        "path": path
                    })
                else:
                    print(f"Warning: No path found from '{start_screen}' to '{target_screen}' for use case '{uc_id}'.")
 
                    # Try several fallback paths if the main path didn't work
                    fallback_attempts = []
                    
                    # Fallback 1: Try from entry_screen
                    if start_screen != entry_screen:
                        print(f"Fallback 1: Trying from entry_screen: '{entry_screen}' to '{target_screen}'")
                        fallback_attempts.append(("entry_screen", entry_screen))
                    
                    # Fallback 2: Try from node[1]
                    if node1_name and node1_name != start_screen and node1_name != entry_screen:
                        print(f"Fallback 2: Trying from node[1]: '{node1_name}' to '{target_screen}'")
                        fallback_attempts.append(("node[1]", node1_name))
                    

                    
                    # Try each fallback path
                    for fallback_name, fallback_screen in fallback_attempts:
                        print(f"Trying fallback path from {fallback_name} ({fallback_screen}) to {target_screen}")
                        path = BFSPathfinder.find_shortest_path(
                            graph_json_path=temp_graph_path,
                            start_screen=fallback_screen,
                            target_screen=target_screen,
                            login_action_filter=login_action_filter,
                            available_roles=available_roles
                        )
                        if path:
                            print(f"Found fallback path using {fallback_name}")
                            break
                        
                        if path:
                            paths_result.append({
                                "use_case_id": uc_id,
                                "screen": target_screen,
                                "path": path
                            })
            
            # Clean up temporary file
            if os.path.exists(temp_graph_path):
                os.remove(temp_graph_path)
                
            return paths_result
            
        except Exception as e:
            print(f"Error in build_paths_with_bfs: {e}")
            # Clean up temporary file if it exists
            if os.path.exists(temp_graph_path):
                os.remove(temp_graph_path)
            return []

    def execute(self):
        # Generate business flow specific screen variables
        print(f"--- Generating Screen Variables for Business Flow {self.business_flow_number} ---")
        self._generate_business_flow_screen_variables()
        print("-" * 30)

        print("--- Generating Flows Data ---")
        flows_data = self.generate_flows()
        if not flows_data:
            print("Flow generation failed. Exiting.")
            return
        print("-" * 30)

        # Now generate shared screen flow graph with flows data
        screen_flows_dict = flows_data.get('screen_flows')
        print("--- Generating Shared Screen Flow Graph ---")
        self._generate_shared_screen_flow_graph(screen_flows_dict)
        print("-" * 30)
        print("--- Building BFS Screen Paths ---")
        bfs_paths_result = self.build_paths_with_bfs(flows_data)
        print("-" * 30)

        # Use only BFS results for output
        final_paths_result = {
            "bfs_paths": bfs_paths_result
        }

        if bfs_paths_result:
            with open(self.screen_paths_output_path, 'w', encoding='utf-8') as f:
                json.dump(final_paths_result, f, indent=4, ensure_ascii=False)
            print(f"Generated screen paths saved to {self.screen_paths_output_path}")

            print("\n--- BFS Path Example ---")
            print(json.dumps(bfs_paths_result[0], indent=4))
            print("-" * 30)
        else:
            print("No screen paths were generated.")
            
        # Print token summary if enabled
        if self.count_token:
            self.print_token_summary()
            
        print("\n📁 Generated Files:")
        print(f"  Business-specific flows: {self.flows_output_path}")
        print(f"  Business-specific paths: {self.screen_paths_output_path}")
        print(f"  Business-specific variables: {self.business_flow_variables_output_path}")
        print(f"  Shared flow graph: {self.shared_graph_image_output_path}")

    def visualize_screen_graph(self, screen_flows_dict: Dict[str, Any], output_path: str):
        """Create and save a screen graph image based on 'before_screen'."""
        if not screen_flows_dict:
            print("No screen flow data provided for visualization.")
            return

        G = nx.DiGraph()
        edges = []
        labels = {node_id: details.get('screen_name', node_id) for node_id, details in screen_flows_dict.items()}

        for screen_id, details in screen_flows_dict.items():
            G.add_node(screen_id)
            before_screen = details.get('before_screen')
            if before_screen:
                if before_screen not in G:
                    G.add_node(before_screen)
                    if before_screen not in labels:
                        labels[before_screen] = before_screen
                    print(f"Warning: Node '{before_screen}' not found in screen_flows keys, added implicitly.")
                edges.append((before_screen, screen_id))

        G.add_edges_from(edges)

        plt.figure(figsize=(12, 8))
        pos = None
        try:
            pos = nx.nx_agraph.graphviz_layout(G, prog='dot')
            print("Using graphviz 'dot' layout for hierarchical structure.")
        except ImportError:
            print("PyGraphviz not found. Falling back to spring_layout.")
            pos = nx.spring_layout(G, k=0.5, iterations=50)
        except Exception as e:
            print(f"Error using graphviz layout: {e}. Falling back to spring_layout.")
            pos = nx.spring_layout(G, k=0.5, iterations=50)

        nx.draw_networkx_nodes(G, pos, node_size=2500, node_color='lightblue', alpha=0.9)
        nx.draw_networkx_edges(G, pos, edgelist=edges, width=2, alpha=0.7, edge_color='black', arrows=True, arrowsize=20)
        nx.draw_networkx_labels(G, pos, labels=labels, font_size=9, font_weight='bold')

        plt.title("Screen Flow Graph (based on 'before_screen')", size=15)
        plt.axis('off')
        plt.tight_layout()

        try:
            plt.savefig(output_path, format="PNG", dpi=150)
            print(f"Screen flow graph saved to {output_path}")
        except Exception as e:
            print(f"Error saving graph image: {e}")
        plt.close()