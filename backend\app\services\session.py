"""
Session management service for user workspaces and state
"""
import os
import json
import asyncio
from typing import Dict, Any, Set, Optional
from datetime import datetime
import logging
from pathlib import Path

from ..core.config import settings

logger = logging.getLogger(__name__)

class SessionManager:
    """Manages user sessions and workspaces"""
    
    def __init__(self):
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.websocket_connections: Dict[str, Set] = {}
        self.step_statuses: Dict[str, Dict[str, str]] = {}  # session_id -> step_id -> status
        
        # Ensure workspace directory exists
        os.makedirs(settings.workspace_root, exist_ok=True)
    
    def create_session(self, session_id: str) -> Dict[str, Any]:
        """Create a new session with workspace"""
        workspace_dir = os.path.join(settings.workspace_root, session_id)
        
        # Create workspace directories
        os.makedirs(workspace_dir, exist_ok=True)
        os.makedirs(os.path.join(workspace_dir, "input"), exist_ok=True)
        os.makedirs(os.path.join(workspace_dir, "output"), exist_ok=True)
        os.makedirs(os.path.join(workspace_dir, "temp"), exist_ok=True)
        
        session_info = {
            "id": session_id,
            "workspace_dir": workspace_dir,
            "created_at": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "active": True
        }
        
        self.sessions[session_id] = session_info
        self.step_statuses[session_id] = {}
        self.websocket_connections[session_id] = set()
        
        logger.info(f"Created session: {session_id}")
        return session_info
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        return self.sessions.get(session_id)
    
    def get_session_workspace(self, session_id: str) -> str:
        """Get workspace directory for a session"""
        if session_id not in self.sessions:
            self.create_session(session_id)
        return self.sessions[session_id]["workspace_dir"]
    
    async def update_step_status(self, session_id: str, step_id: str, status: str):
        """Update step status and notify connected clients"""
        if session_id not in self.step_statuses:
            self.step_statuses[session_id] = {}
        
        self.step_statuses[session_id][step_id] = status
        
        # Notify connected WebSocket clients
        await self.broadcast_to_session(session_id, {
            "type": "step_update",
            "data": {
                "step_id": step_id,
                "status": status,
                "timestamp": datetime.now().isoformat()
            }
        })
    
    def get_step_status(self, session_id: str, step_id: str) -> str:
        """Get current status of a step"""
        return self.step_statuses.get(session_id, {}).get(step_id, "pending")
    
    def get_all_step_statuses(self, session_id: str) -> Dict[str, str]:
        """Get all step statuses for a session"""
        return self.step_statuses.get(session_id, {}).copy()
    
    async def add_websocket_connection(self, session_id: str, websocket):
        """Add a WebSocket connection to a session"""
        if session_id not in self.websocket_connections:
            self.websocket_connections[session_id] = set()
        self.websocket_connections[session_id].add(websocket)
        logger.info(f"Added WebSocket connection to session: {session_id}")
    
    async def remove_websocket_connection(self, session_id: str, websocket):
        """Remove a WebSocket connection from a session"""
        if session_id in self.websocket_connections:
            self.websocket_connections[session_id].discard(websocket)
            if not self.websocket_connections[session_id]:
                del self.websocket_connections[session_id]
        logger.info(f"Removed WebSocket connection from session: {session_id}")
    
    async def broadcast_to_session(self, session_id: str, message: Dict[str, Any]):
        """Broadcast a message to all WebSocket connections in a session"""
        if session_id not in self.websocket_connections:
            return
        
        connections = self.websocket_connections[session_id].copy()
        for websocket in connections:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to WebSocket: {e}")
                # Remove failed connection
                self.websocket_connections[session_id].discard(websocket)
    
    async def send_terminal_output(self, session_id: str, output: str):
        """Send terminal output to connected clients"""
        await self.broadcast_to_session(session_id, {
            "type": "terminal_output",
            "data": {
                "output": output,
                "timestamp": datetime.now().isoformat()
            }
        })
    
    async def send_cost_update(self, session_id: str, cost: float):
        """Send cost update to connected clients"""
        await self.broadcast_to_session(session_id, {
            "type": "cost_update",
            "data": {
                "cost": cost,
                "timestamp": datetime.now().isoformat()
            }
        })
    
    def list_workspace_files(self, session_id: str, subdirectory: str = "") -> Dict[str, Any]:
        """List files in the session workspace"""
        workspace_dir = self.get_session_workspace(session_id)
        target_dir = os.path.join(workspace_dir, subdirectory) if subdirectory else workspace_dir
        
        if not os.path.exists(target_dir):
            return {"files": [], "directories": []}
        
        files = []
        directories = []
        
        for item in os.listdir(target_dir):
            item_path = os.path.join(target_dir, item)
            if os.path.isfile(item_path):
                files.append({
                    "name": item,
                    "path": os.path.relpath(item_path, workspace_dir),
                    "size": os.path.getsize(item_path),
                    "modified": os.path.getmtime(item_path)
                })
            elif os.path.isdir(item_path):
                directories.append({
                    "name": item,
                    "path": os.path.relpath(item_path, workspace_dir)
                })
        
        return {"files": files, "directories": directories}
    
    def cleanup_session(self, session_id: str):
        """Clean up session data"""
        if session_id in self.sessions:
            del self.sessions[session_id]
        if session_id in self.step_statuses:
            del self.step_statuses[session_id]
        if session_id in self.websocket_connections:
            del self.websocket_connections[session_id]
        logger.info(f"Cleaned up session: {session_id}")

# Global instance
session_manager = SessionManager()
